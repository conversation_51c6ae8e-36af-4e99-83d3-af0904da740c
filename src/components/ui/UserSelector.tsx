import React, { useMemo } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useQuery } from '@tanstack/react-query'
import { getUsersApiV1UsersGetOptions } from '@/services/api'

interface UserSelectorProps {
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
}

export const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onValueChange,
  placeholder = "选择用户",
  disabled = false
}) => {
      // 获取用户列表
  const { data: usersData } = useQuery({
    ...getUsersApiV1UsersGetOptions()
  })

  const users = useMemo(() => usersData?.map((user) => ({
    id: user.id,
    name: user.full_name || user.username,
    code: user.username
  })) || [], [usersData])


  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {users.map((user) => (
          <SelectItem key={user.id} value={user.id.toString()}>
            {user.name} ({user.code})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Alias for EmployeeSelector
export const EmployeeSelector = UserSelector
