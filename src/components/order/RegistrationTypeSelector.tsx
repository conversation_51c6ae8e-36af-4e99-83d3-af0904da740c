import React from 'react'
import { Controller } from 'react-hook-form'
import type { Control } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import type { RegistrationFormData } from './RegistrationDialog'

// 登记类型常量
const REGISTRATION_TYPES = [
  { value: 'ALL', label: '整单录入'},
  { value: 'PART', label: '分床录入'},
  { value: 'BUNDLER', label: '分扎录入'}
]

interface RegistrationTypeSelectorProps {
  control: Control<RegistrationFormData>
  availableTypes: string[]
  onTypeChange: (type: string) => void
}

export const RegistrationTypeSelector: React.FC<RegistrationTypeSelectorProps> = ({
  control,
  availableTypes,
  onTypeChange
}) => {
  return (
    <Controller
      control={control}
      name="registrationType"
      rules={{ required: '请选择登记类型' }}
      render={({ field, fieldState }) => (
        <FormItem>
          <FormLabel>登记类型</FormLabel>
          <FormControl>
            <div className="grid grid-cols-3 gap-4">
              {availableTypes.map((type) => {
                const typeConfig = REGISTRATION_TYPES.find(t => t.value === type)
                if (!typeConfig) return null
                
                return (
                  <div key={type} className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id={`registration-type-${type}`}
                      name="registrationType"
                      value={type}
                      checked={field.value === type}
                      onChange={() => {
                        field.onChange(type)
                        onTypeChange(type)
                      }}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                    />
                    <Label htmlFor={`registration-type-${type}`} className="text-sm font-normal cursor-pointer">
                      {typeConfig.label}
                    </Label>
                  </div>
                )
              })}
            </div>
          </FormControl>
          {fieldState.error && <FormMessage>{fieldState.error.message}</FormMessage>}
        </FormItem>
      )}
    />
  )
}
