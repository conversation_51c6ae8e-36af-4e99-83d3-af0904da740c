import React, { useMemo } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { getAvailableRegistrationDataOptions } from '@/services/@tanstack/react-query.gen'
import type { RegistrationFormData } from './RegistrationDialog'
import type { OrderCraftRouteResponseDto } from '@/services/types.gen'

interface OrderPartSelectorProps {
  orderNo?: string
  selectedRoute?: OrderCraftRouteResponseDto
}

export const OrderPartSelector: React.FC<OrderPartSelectorProps> = ({
  orderNo,
  selectedRoute
}) => {
  const { control, watch, setValue } = useFormContext<RegistrationFormData>()
  const registrationType = watch('registrationType')

  // 获取可用的登记数据（分床）
  const { data: availableRegistrationData } = useQuery({
    ...getAvailableRegistrationDataOptions({
      path: { order_no: orderNo || '' },
      query: {
        craft_route_id: selectedRoute?.id,
        include_completed_routes: false,
        granularity_filter: 'part'
      }
    }),
    enabled: !!orderNo && !!selectedRoute?.id && registrationType === 'PART'
  })

  const orderParts = useMemo(() => availableRegistrationData?.order_parts || [], [availableRegistrationData])

  // 计算并设置完成数量的辅助函数
  const updateCompletedQuantity = (selectedParts: string[]) => {
    const totalAvailableQuantity = orderParts
      .filter(part => selectedParts.includes(part.order_part_no || ''))
      .reduce((sum, part) => sum + (part.available_quantity || 0), 0)

    // 如果没有选择任何分床，设置为默认值1，否则设置为总可用数量
    setValue('completedQuantity', selectedParts.length === 0 ? 1 : totalAvailableQuantity)
  }

  // 只在登记类型为 PART 时显示
  if (registrationType !== 'PART') {
    return null
  }

  
  return (
    <Controller
      control={control}
      name="selectedParts"
      render={({field}) => (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">选择分床</h3>
          <p className="text-sm text-muted-foreground">请选择要登记完成的分床，可以选择多个。</p>
          {orderParts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>当前订单暂无可登记的分床数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {orderParts.map((part) => {
                const isSelected = field.value?.includes(part.order_part_no || '')
                const availableQuantity = part.available_quantity || 0
                const totalQuantity = part.total_quantity || 0
                const registeredQuantity = part.registered_quantity || 0
                
                return (
                  <Card 
                    key={part.order_part_no} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:border-primary/50'
                    }`}
                    onClick={
                      (e) => {
                        e.stopPropagation()
                        let newSelected: string[]
                        if(field.value?.includes(part.order_part_no || '')) {
                          newSelected = field.value.filter(p => p !== part.order_part_no)
                        } else {
                          newSelected = [...field.value, part.order_part_no]
                        }
                        field.onChange(newSelected)
                        updateCompletedQuantity(newSelected)
                      }
                    }
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                              let newSelected: string[]
                              if(checked) {
                                newSelected = [...field.value, part.order_part_no]
                              } else {
                                newSelected = field.value.filter(p => p !== part.order_part_no)
                              }
                              field.onChange(newSelected)
                              updateCompletedQuantity(newSelected)
                            }}
                            onClick={(e) => e.stopPropagation()}
                          />
                          <span className="font-medium text-lg">{part.order_part_no}</span>
                        </div>
                        <Badge variant={availableQuantity > 0 ? 'default' : 'secondary'}>
                          {availableQuantity > 0 ? '可登记' : '已完成'}
                        </Badge>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">总数量:</span>
                          <span className="font-medium">{totalQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">已登记:</span>
                          <span className="font-medium">{registeredQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">可登记:</span>
                          <span className="font-medium text-primary">{availableQuantity}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      )}
    />
  )
}
