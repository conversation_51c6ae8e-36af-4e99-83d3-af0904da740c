import React from 'react'
import { Controller } from 'react-hook-form'
import type { Control } from 'react-hook-form'
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import type { RegistrationFormData } from './RegistrationDialog'

interface OrderPart {
  order_part_no?: string
  available_quantity?: number
  total_quantity?: number
  registered_quantity?: number
}

interface OrderPartSelectorProps {
  control: Control<RegistrationFormData>
  orderParts: OrderPart[]
  selectedParts: string[]
  onPartSelection: (orderPartNo: string, checked: boolean) => void
}

export const OrderPartSelector: React.FC<OrderPartSelectorProps> = ({
  control,
  orderParts,
  selectedParts,
  onPartSelection
}) => {
  return (
    <Controller
      control={control}
      name="selectedParts"
      render={() => (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">选择分床</h3>
          <p className="text-sm text-muted-foreground">请选择要登记完成的分床，可以选择多个。</p>
          {orderParts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>当前订单暂无可登记的分床数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {orderParts.map((part) => {
                const isSelected = selectedParts.includes(part.order_part_no || '')
                const availableQuantity = part.available_quantity || 0
                const totalQuantity = part.total_quantity || 0
                const registeredQuantity = part.registered_quantity || 0
                
                return (
                  <Card 
                    key={part.order_part_no} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:border-primary/50'
                    }`}
                    onClick={() => onPartSelection(part.order_part_no || '', !isSelected)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => 
                              onPartSelection(part.order_part_no || '', checked as boolean)
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <span className="font-medium text-lg">{part.order_part_no}</span>
                        </div>
                        <Badge variant={availableQuantity > 0 ? 'default' : 'secondary'}>
                          {availableQuantity > 0 ? '可登记' : '已完成'}
                        </Badge>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">总数量:</span>
                          <span className="font-medium">{totalQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">已登记:</span>
                          <span className="font-medium">{registeredQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">可登记:</span>
                          <span className="font-medium text-primary">{availableQuantity}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      )}
    />
  )
}
