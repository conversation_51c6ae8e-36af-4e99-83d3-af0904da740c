import React from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { getAvailableRegistrationDataOptions } from '@/services/@tanstack/react-query.gen'
import type { RegistrationFormData } from './RegistrationDialog'
import type { OrderCraftRouteResponseDto } from '@/services/types.gen'



interface OrderBundleSelectorProps {
  orderNo?: string
  selectedRoute?: OrderCraftRouteResponseDto
}

export const OrderBundleSelector: React.FC<OrderBundleSelectorProps> = ({
  orderNo,
  selectedRoute
}) => {
  const { control, watch, setValue } = useFormContext<RegistrationFormData>()
  const registrationType = watch('registrationType')

  // 获取可用的登记数据（分扎）
  const { data: availableRegistrationData } = useQuery({
    ...getAvailableRegistrationDataOptions({
      path: { order_no: orderNo || '' },
      query: {
        craft_route_id: selectedRoute?.id,
        include_completed_routes: false,
        granularity_filter: 'bundle'
      }
    }),
    enabled: !!orderNo && !!selectedRoute?.id && registrationType === 'BUNDLER'
  })

  // 从可用登记数据中提取分扎信息
  const orderParts = availableRegistrationData?.order_parts || []
  const orderBundles = orderParts.flatMap(part =>
    (part.order_bundles || []).map(bundle => ({
      ...bundle,
      order_part_no: part.order_part_no // 添加所属床号信息
    }))
  ) || []

  // 计算并设置完成数量的辅助函数
  const updateCompletedQuantity = (selectedBundles: string[]) => {
    const totalAvailableQuantity = orderBundles
      .filter(bundle => selectedBundles.includes(bundle.order_bundle_no || ''))
      .reduce((sum, bundle) => sum + (bundle.available_quantity || 0), 0)

    // 如果没有选择任何分扎，设置为默认值1，否则设置为总可用数量
    setValue('completedQuantity', selectedBundles.length === 0 ? 1 : totalAvailableQuantity)
  }

  // 只在登记类型为 BUNDLER 时显示
  if (registrationType !== 'BUNDLER') {
    return null
  }
  return (
    <Controller
      control={control}
      name="selectedBundles"
      render={({field}) => (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">选择分扎</h3>
          <p className="text-sm text-muted-foreground">
            请选择要登记完成的分扎，可以选择多个。扎号已简化显示（显示后6位）。
          </p>
          {orderBundles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>当前订单暂无可登记的分扎数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {orderBundles.map((bundle) => {
                const isSelected = field.value?.includes(bundle.order_bundle_no || '')
                const availableQuantity = bundle.available_quantity || 0
                const totalQuantity = bundle.total_quantity || 0
                const registeredQuantity = bundle.registered_quantity || 0
                
                // 只显示扎号的后6位
                const displayBundleNo = bundle.order_bundle_no ? 
                  (bundle.order_bundle_no.length > 6 ? 
                    '...' + bundle.order_bundle_no.slice(-6) : 
                    bundle.order_bundle_no) : ''
                
                return (
                  <Card 
                    key={bundle.order_bundle_no} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:border-primary/50'
                    }`}
                    onClick={() => {
                      let newSelected: string[]
                      if(field.value?.includes(bundle.order_bundle_no || '')) {
                        newSelected = field.value.filter(p => p !== bundle.order_bundle_no)
                      } else {
                        newSelected = [...field.value, bundle.order_bundle_no]
                      }
                      field.onChange(newSelected)
                      updateCompletedQuantity(newSelected)
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                              let newSelected: string[]
                              if(checked) {
                                newSelected = [...field.value, bundle.order_bundle_no]
                              } else {
                                newSelected = field.value.filter(p => p !== bundle.order_bundle_no)
                              }
                              field.onChange(newSelected)
                              updateCompletedQuantity(newSelected)
                            }}
                            onClick={(e) => e.stopPropagation()}
                          />
                          <span className="font-medium text-lg" title={bundle.order_bundle_no}>
                            {displayBundleNo}
                          </span>
                        </div>
                        <Badge variant={availableQuantity > 0 ? 'default' : 'secondary'}>
                          {availableQuantity > 0 ? '可登记' : '已完成'}
                        </Badge>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">总数量:</span>
                          <span className="font-medium">{totalQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">已登记:</span>
                          <span className="font-medium">{registeredQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">可登记:</span>
                          <span className="font-medium text-primary">{availableQuantity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">所属床号:</span>
                          <span className="font-medium text-xs">{bundle.order_part_no || '-'}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      )}
    />
  )
}
