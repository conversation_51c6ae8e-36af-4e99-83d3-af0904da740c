import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Settings, ChevronDown, User, Clock, Target, FileText, Users, Loader2, Play } from 'lucide-react'
import { RegistrationDialog } from './RegistrationDialog'
import { searchCraftInstancesApiV1CraftInstancesSearchGetOptions, startOrderApiV1OrdersOrderNoStartPostMutation } from '@/services/@tanstack/react-query.gen'
import type { OrderCraftResponseDto, OrderCraftRouteResponseDto } from '@/services/types.gen'
import { toast } from 'sonner'

interface OrderCraftStepsProps {
  orderCrafts: OrderCraftResponseDto[]
  orderNo?: string // 订单号，用于获取分床分扎数据
  orderStatus?: string // 订单状态，用于显示开始按钮
  showEditButton?: boolean
  onShowEdit?: () => void
}

// 简化的登记数据接口（用于向后兼容）
interface RegistrationData {
  routeId: number
  registrationType: string
  unitIdentifier: string
  orderPartNos: string[]
  orderBundleNos: string[]
  completionPerson: string
  startTime: string
  completionTime: string
  notes: string
}

// 根据状态确定步骤状态
const getCraftStatus = (craft: OrderCraftResponseDto): 'pending' | 'current' | 'completed' => {
  if (craft.completed_at) return 'completed'
  if (craft.started_at) return 'current'
  return 'pending'
}

// 根据状态确定工艺路线状态
const getRouteStatus = (route: OrderCraftRouteResponseDto): 'pending' | 'current' | 'completed' => {
  if (route.completed_at) return 'completed'
  if (route.started_at) return 'current'
  return 'pending'
}

// 格式化持续时间
const formatDuration = (minutes?: number | null): string => {
  if (!minutes) return '-'
  if (minutes < 60) return `${minutes}分钟`
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
}

// 工艺路线组件
const CraftRouteItem: React.FC<{
  route: OrderCraftRouteResponseDto,
  orderNo?: string,
  onStartRegistration: (route: OrderCraftRouteResponseDto) => void
}> = ({ route, orderNo, onStartRegistration }) => {
  const [showInstances, setShowInstances] = useState(false)
  const status = getRouteStatus(route)

  // 获取该工艺路线的实例
  const { data: craftInstancesData, isLoading: instancesLoading, error: instancesError } = useQuery({
    ...searchCraftInstancesApiV1CraftInstancesSearchGetOptions({
      query: {
        order_no: orderNo,
        limit: 100 // 获取足够多的实例
      }
    }),
    enabled: !!orderNo && showInstances,
    staleTime: 30000, // 30秒缓存
  })

  // 过滤出属于当前工艺路线的实例
  const routeInstances = craftInstancesData?.instances?.filter(
    instance => instance.order_craft_route_id === route.id
  ) || []

  const toggleInstances = () => {
    setShowInstances(!showInstances)
  }

  return (
    <div className="border rounded-lg bg-muted/20">
      <div className="flex items-center gap-4 p-3">
        <div className={`w-2 h-2 rounded-full shrink-0 ${
          status === 'completed' ? 'bg-green-500' :
          status === 'current' ? 'bg-blue-500' :
          'bg-gray-300'
        }`} />

        <div className="flex-1 space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{route.skill_name || route.skill_code}</span>
            <Badge variant="outline" className="text-xs">{route.skill_code}</Badge>
            {route.is_required && <Badge variant="secondary" className="text-xs">必需</Badge>}
          </div>

          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {route.assigned_user_name && (
              <span className="flex items-center gap-1">
                <User className="w-3 h-3" />
                {route.assigned_user_name}
              </span>
            )}
            {route.estimated_duration_minutes && (
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                预计: {formatDuration(route.estimated_duration_minutes)}
              </span>
            )}
            {route.actual_duration_minutes && (
              <span className="flex items-center gap-1">
                <Target className="w-3 h-3" />
                实际: {formatDuration(route.actual_duration_minutes)}
              </span>
            )}
            {routeInstances.length > 0 && (
              <span className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                {routeInstances.length} 个实例
              </span>
            )}
          </div>

          {route.notes && (
            <p className="text-xs text-muted-foreground">{route.notes}</p>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 实例查看按钮 */}
          {orderNo && (
            <Button
              size="sm"
              variant="ghost"
              onClick={toggleInstances}
              className="h-8 px-2 text-xs"
              title={showInstances ? "隐藏实例" : "查看实例"}
            >
              <Users className="w-3 h-3 mr-1" />
              {routeInstances.length}
              <ChevronDown className={`w-3 h-3 ml-1 transition-transform ${showInstances ? 'rotate-180' : ''}`} />
            </Button>
          )}

          {/* 登记按钮 */}
          {route.registration_types && route.registration_types.length > 0 && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onStartRegistration(route)}
              className="h-8 px-2 text-xs text-blue-600 border-blue-200 hover:bg-blue-50"
              title="登记此工序"
            >
              <FileText className="w-3 h-3 mr-1" />
              登记
            </Button>
          )}

          <Badge variant={
            status === 'completed' ? 'default' :
            status === 'current' ? 'secondary' :
            'outline'
          }>
            {status === 'completed' ? '已完成' :
             status === 'current' ? '进行中' :
             '待开始'}
          </Badge>
        </div>
      </div>

      {/* 工艺实例详情 */}
      {showInstances && (
        <div className="border-t bg-background/50 p-3">
          {instancesLoading && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">加载实例中...</span>
            </div>
          )}

          {instancesError && (
            <div className="text-center py-4">
              <p className="text-sm text-red-600">加载实例失败</p>
            </div>
          )}

          {!instancesLoading && !instancesError && routeInstances.length === 0 && (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground">暂无工艺实例</p>
            </div>
          )}

          {!instancesLoading && !instancesError && routeInstances.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">工艺实例 ({routeInstances.length})</h4>
              <div className="grid gap-2">
                {routeInstances.map((instance) => (
                  <div key={instance.id} className="flex items-center justify-between p-2 bg-background rounded border">
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="font-medium">{instance.worker_name || `工人 ${instance.worker_user_id}`}</span>
                        <Badge variant="outline" className="text-xs">
                          {instance.completion_granularity === 'order' ? '整单' :
                           instance.completion_granularity === 'bed' ? '分床' : '分扎'}
                        </Badge>
                        {instance.quality_level && (
                          <Badge variant="secondary" className="text-xs">{instance.quality_level}</Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {instance.completed_quantity && (
                          <span>数量: {instance.completed_quantity}</span>
                        )}
                        {instance.started_at && (
                          <span>开始: {new Date(instance.started_at).toLocaleString()}</span>
                        )}
                        {instance.completed_at && (
                          <span>完成: {new Date(instance.completed_at).toLocaleString()}</span>
                        )}
                      </div>

                      {instance.order_part_nos && instance.order_part_nos.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          床号: {instance.order_part_nos.join(', ')}
                        </div>
                      )}

                      {instance.order_bundle_nos && instance.order_bundle_nos.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          扎号: {instance.order_bundle_nos.join(', ')}
                        </div>
                      )}

                      {instance.notes && (
                        <div className="text-xs text-muted-foreground">
                          备注: {instance.notes}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-1">
                      <Badge variant={instance.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
                        {instance.status === 'completed' ? '已完成' : instance.status || '进行中'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export const OrderCraftSteps: React.FC<OrderCraftStepsProps> = ({
  orderCrafts,
  orderNo,
  orderStatus,
  showEditButton = false,
  onShowEdit,
}) => {
  const [expandedCraft, setExpandedCraft] = useState<number | null>(null)
  const queryClient = useQueryClient()
  
  // 登记相关状态
  const [registrationDialogOpen, setRegistrationDialogOpen] = useState(false)
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    routeId: 0,
    registrationType: '',
    unitIdentifier: '',
    orderPartNos: [],
    orderBundleNos: [],
    completionPerson: '',
    startTime: '',
    completionTime: '',
    notes: ''
  })
  
  // 开始订单的 mutation
  const startOrderMutation = useMutation({
    ...startOrderApiV1OrdersOrderNoStartPostMutation(),
    onSuccess: () => {
      toast.success('订单已开始')
      // 刷新相关数据
      queryClient.invalidateQueries({
        queryKey: ['api', 'v1', 'orders', orderNo]
      })
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.length > 0 &&
                 typeof queryKey[0] === 'object' &&
                 queryKey[0] !== null &&
                 '_id' in queryKey[0] &&
                 (queryKey[0]._id === 'getOrderByOrderNoMainApiV1OrdersOrderNoGet' ||
                  queryKey[0]._id === 'getOrderCraftsByOrderApiV1OrdersOrderNoCraftsGet')
        }
      })
    },
    onError: (error) => {
      console.error('开始订单失败:', error)
      toast.error('开始订单失败')
    }
  })

  const toggleCraft = (craftId: number) => {
    // If clicking on the already expanded craft, close it; otherwise, open the new one
    setExpandedCraft(expandedCraft === craftId ? null : craftId)
  }

  // 开始订单
  const handleStartOrder = () => {
    if (!orderNo) {
      toast.error('订单号不能为空')
      return
    }

    startOrderMutation.mutate({
      path: { order_no: orderNo }
    })
  }

  // 开始登记
  const startRegistration = (route: OrderCraftRouteResponseDto) => {
    const now = new Date()
    const currentDateTime = now.toISOString().slice(0, 16) // YYYY-MM-DDTHH:mm format
    
    setRegistrationData({
      routeId: route.id,
      registrationType: route.registration_types?.[0] || '', // 默认选择第一个登记类型
      unitIdentifier: '',
      orderPartNos: [],
      orderBundleNos: [],
      completionPerson: '',
      startTime: currentDateTime,
      completionTime: currentDateTime,
      notes: ''
    })
    setRegistrationDialogOpen(true)
  }

  // 提交登记
  const submitRegistration = async () => {
    try {
      // 这里应该调用API提交登记数据
      console.log('提交登记数据:', registrationData)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 成功后关闭对话框并重置数据
      setRegistrationDialogOpen(false)
      setRegistrationData({
        routeId: 0,
        registrationType: '',
        unitIdentifier: '',
        orderPartNos: [],
        orderBundleNos: [],
        completionPerson: '',
        startTime: '',
        completionTime: '',
        notes: ''
      })
      
      // 这里可以添加成功提示
      console.log('登记成功')
    } catch (error) {
      console.error('登记失败:', error)
    }
  }

  // 取消登记
  const cancelRegistration = () => {
    setRegistrationDialogOpen(false)
    setRegistrationData({
      routeId: 0,
      registrationType: '',
      unitIdentifier: '',
      orderPartNos: [],
      orderBundleNos: [],
      completionPerson: '',
      startTime: '',
      completionTime: '',
      notes: ''
    })
  }

  // 获取当前选择路线的信息
  const getSelectedRoute = (): OrderCraftRouteResponseDto | undefined => {
    for (const craft of orderCrafts) {
      const route = craft.order_craft_routes?.find(r => r.id === registrationData.routeId)
      if (route) return route
    }
    return undefined
  }
  
  // 按顺序排序工艺
  const sortedCrafts = [...orderCrafts].sort((a, b) => a.order - b.order)

  if (!orderCrafts || orderCrafts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              工艺流程
            </CardTitle>
            {showEditButton && (
              <Button size="sm" variant="outline" onClick={onShowEdit}>
                添加工艺
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">暂无工艺配置</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            工艺流程
          </CardTitle>
        <div className="flex items-center gap-2">
          {/* 开始订单按钮 - 仅在订单状态为 pending 时显示 */}
          {orderStatus === 'pending' && orderNo && (
            <Button
              size="sm"
              variant="default"
              onClick={handleStartOrder}
              disabled={startOrderMutation.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              {startOrderMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  开始中...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  开始订单
                </>
              )}
            </Button>
          )}

          {showEditButton && (
            <Button size="sm" variant="outline" onClick={onShowEdit}>
              编辑工艺
            </Button>
          )}
        </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* 横向步骤布局 */}
        <div className="space-y-6">
          {/* 步骤进度条 */}
          <div className="relative">
            <div className="flex items-center justify-between">
              {sortedCrafts.map((craft, index) => {
                const status = getCraftStatus(craft)
                const isLastStep = index === sortedCrafts.length - 1
                const routes = craft.order_craft_routes || []
                const isExpanded = expandedCraft === craft.id
                
                return (
                  <div key={craft.id} className="flex items-center">
                    {/* 步骤指示器 - 可点击 */}
                    <div className="flex flex-col items-center">
                      <Button
                        variant="ghost"
                        className={`w-8 h-8 rounded-full p-0 flex items-center justify-center text-sm font-medium transition-colors ${
                          status === 'completed' ? 'bg-green-500 text-white hover:bg-green-600' : 
                          status === 'current' ? 'bg-blue-500 text-white hover:bg-blue-600' : 
                          'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        } ${isExpanded ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
                        onClick={() => toggleCraft(craft.id)}
                      >
                        {craft.order}
                      </Button>
                      <div className="mt-2 text-center">
                        <div className="text-sm font-medium max-w-24 break-words">
                          {craft.craft_name || craft.craft_code}
                        </div>
                        <Badge 
                          variant={
                            status === 'completed' ? 'default' : 
                            status === 'current' ? 'secondary' : 
                            'outline'
                          }
                          className="mt-1 text-xs"
                        >
                          {status === 'completed' ? '已完成' : 
                           status === 'current' ? '进行中' : 
                           '待开始'}
                        </Badge>
                        {routes.length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {routes.filter(r => getRouteStatus(r) === 'completed').length}/{routes.length} 工序
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* 连接线 */}
                    {!isLastStep && (
                      <div className={`flex-1 h-px mx-4 ${
                        status === 'completed' ? 'bg-green-500' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>
          
          {/* 展开的步骤详情 */}
          {sortedCrafts.map((craft) => {
            const status = getCraftStatus(craft)
            const routes = craft.order_craft_routes || []
            const isExpanded = expandedCraft === craft.id
            
            if (!isExpanded) return null
            
            return (
              <div key={`detail-${craft.id}`} className={`border rounded-lg p-4 transition-colors ${
                status === 'completed' ? 'bg-green-50 border-green-200' : 
                status === 'current' ? 'bg-blue-50 border-blue-200' : 
                'bg-gray-50 border-gray-200'
              }`}>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-semibold">
                        {craft.craft_name || craft.craft_code}
                      </h3>
                      <Badge variant="outline">{craft.craft_code}</Badge>
                      {craft.is_required && <Badge variant="secondary">必需</Badge>}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={
                        status === 'completed' ? 'default' : 
                        status === 'current' ? 'secondary' : 
                        'outline'
                      }>
                        {status === 'completed' ? '已完成' : 
                         status === 'current' ? '进行中' : 
                         '待开始'}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleCraft(craft.id)}
                      >
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">工序顺序:</span>
                      <span className="ml-2 font-medium">第 {craft.order} 道</span>
                    </div>
                    {craft.estimated_duration_hours && (
                      <div>
                        <span className="text-muted-foreground">预计时长:</span>
                        <span className="ml-2 font-medium">{craft.estimated_duration_hours}小时</span>
                      </div>
                    )}
                    {craft.actual_duration_hours && (
                      <div>
                        <span className="text-muted-foreground">实际时长:</span>
                        <span className="ml-2 font-medium">{craft.actual_duration_hours}小时</span>
                      </div>
                    )}
                    {craft.completion_percentage !== undefined && (
                      <div>
                        <span className="text-muted-foreground">完成度:</span>
                        <span className="ml-2 font-medium">{craft.completion_percentage}%</span>
                      </div>
                    )}
                  </div>
                  
                  {(craft.started_at || craft.completed_at) && (
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {craft.started_at && (
                        <span>开始时间: {new Date(craft.started_at).toLocaleString()}</span>
                      )}
                      {craft.completed_at && (
                        <span>完成时间: {new Date(craft.completed_at).toLocaleString()}</span>
                      )}
                    </div>
                  )}
                  
                  {craft.notes && (
                    <div className="text-sm">
                      <span className="text-muted-foreground">备注:</span>
                      <span className="ml-2">{craft.notes}</span>
                    </div>
                  )}
                  
                  {/* 工艺路线详情 */}
                  {routes.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-muted-foreground">工序详情</h4>
                        <span className="text-xs text-muted-foreground">
                          {routes.filter(r => getRouteStatus(r) === 'completed').length} / {routes.length} 已完成
                        </span>
                      </div>
                      
                      {/* 工序进度条 */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>工序进度</span>
                          <span>{Math.round((routes.filter(r => getRouteStatus(r) === 'completed').length / routes.length) * 100)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${(routes.filter(r => getRouteStatus(r) === 'completed').length / routes.length) * 100}%` 
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid gap-2">
                        {routes
                          .sort((a, b) => a.order - b.order)
                          .map((route) => (
                            <CraftRouteItem
                              key={route.id}
                              route={route}
                              orderNo={orderNo}
                              onStartRegistration={startRegistration}
                            />
                          ))
                        }
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
      
      {/* 登记对话框 */}
      <RegistrationDialog
        open={registrationDialogOpen}
        onOpenChange={setRegistrationDialogOpen}
        selectedRoute={getSelectedRoute()}
        orderNo={orderNo}
        onSubmit={submitRegistration}
        onCancel={cancelRegistration}
      />
    </Card>
  )
}
