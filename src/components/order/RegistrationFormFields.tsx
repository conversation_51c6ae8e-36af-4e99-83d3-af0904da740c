import React from 'react'
import type { Control } from 'react-hook-form'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import type { RegistrationFormData } from './RegistrationDialog'

interface Employee {
  id: number
  name: string
  code: string
}

interface RegistrationFormFieldsProps {
  control: Control<RegistrationFormData>
  employees: Employee[]
}

export const RegistrationFormFields: React.FC<RegistrationFormFieldsProps> = ({
  control,
  employees
}) => {
  return (
    <>
      {/* 完成人和开始时间 */}
      <div className="grid grid-cols-2 gap-6">
        <FormField
          control={control}
          name="completionPerson"
          rules={{ required: '请选择完成人' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>完成人</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择完成人" />
                  </SelectTrigger>
                  <SelectContent>
                    {employees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id.toString()}>
                        {employee.name} ({employee.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="startTime"
          rules={{ required: '请选择开始时间' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>开始时间</FormLabel>
              <FormControl>
                <Input
                  type="datetime-local"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 完成数量和质量等级 */}
      <div className="grid grid-cols-2 gap-6">
        <FormField
          control={control}
          name="completedQuantity"
          rules={{ required: '请输入完成数量', min: { value: 1, message: '完成数量必须大于0' } }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>完成数量</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="qualityLevel"
          rules={{ required: '请选择质量等级' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>质量等级</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择质量等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A">A级 (优秀)</SelectItem>
                    <SelectItem value="B">B级 (良好)</SelectItem>
                    <SelectItem value="C">C级 (合格)</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 备注 */}
      <FormField
        control={control}
        name="notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>备注</FormLabel>
            <FormControl>
              <Textarea
                {...field}
                placeholder="输入备注信息（可选）"
                rows={3}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}
