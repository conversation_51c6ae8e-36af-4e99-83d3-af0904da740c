// This file is auto-generated by @hey-api/openapi-ts

/**
 * AddUserToFactoryDTO
 * DTO for adding user to factory.
 */
export type AddUserToFactoryDto = {
    /**
     * User Id
     * User ID to add
     */
    user_id: number;
    /**
     * Factory Role
     * Initial role in factory
     */
    factory_role?: string;
    /**
     * Start Date
     * Start date (ISO format)
     */
    start_date?: string | null;
};

/**
 * AddUserWithFactoryResponseDTO
 * Response DTO for add user with factory operation.
 */
export type AddUserWithFactoryResponseDto = {
    /**
     * Success
     * Whether operation succeeded
     */
    success: boolean;
    /**
     * Message
     * Result message
     */
    message: string;
    /**
     * User Id
     * User ID (if created or found)
     */
    user_id?: number | null;
    /**
     * User Created
     * Whether a new user was created
     */
    user_created: boolean;
    /**
     * Factory Relationship Created
     * Whether factory relationship was created
     */
    factory_relationship_created: boolean;
    /**
     * Skills Assigned
     * Number of skills successfully assigned
     */
    skills_assigned?: number;
    /**
     * Details
     * Additional operation details
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * AddUsersToFactoryDTO
 * DTO for adding multiple users to factory.
 */
export type AddUsersToFactoryDto = {
    /**
     * Users
     * List of users to add
     */
    users: Array<AddUserToFactoryDto>;
};

/**
 * AssignSkillDTO
 * DTO for assigning skill to user.
 */
export type AssignSkillDto = {
    /**
     * User Id
     * User ID
     */
    user_id: number;
    /**
     * Skill Id
     * Skill ID
     */
    skill_id: number;
    /**
     * Proficiency Level
     * Initial proficiency level
     */
    proficiency_level?: string;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
};

/**
 * AssignSkillsDTO
 * DTO for assigning multiple skills to user.
 */
export type AssignSkillsDto = {
    /**
     * User Id
     * User ID
     */
    user_id: number;
    /**
     * Skills
     * List of skills to assign
     */
    skills: Array<AssignSkillDto>;
};

/**
 * AvailableBundleDTO
 * 可登记的订单扎信息
 */
export type AvailableBundleDto = {
    /**
     * Order Bundle No
     * 订单扎号
     */
    order_bundle_no: string;
    /**
     * Total Quantity
     * 扎总数量
     */
    total_quantity: number;
    /**
     * Registered Quantity
     * 已登记数量
     */
    registered_quantity?: number;
    /**
     * Available Quantity
     * 可登记数量
     */
    available_quantity: number;
};

/**
 * AvailableCraftRouteDTO
 * 可登记的工艺路线信息
 */
export type AvailableCraftRouteDto = {
    /**
     * Order Craft Route Id
     * 订单工艺路线ID
     */
    order_craft_route_id: number;
    /**
     * Craft Route Name
     * 工艺路线名称
     */
    craft_route_name: string;
    /**
     * Skill Code
     * 技能代码
     */
    skill_code: string;
    /**
     * Skill Name
     * 技能名称
     */
    skill_name?: string | null;
    /**
     * Route Status
     * 路线状态
     */
    route_status: string;
    /**
     * Total Quantity
     * 路线总数量
     */
    total_quantity: number;
    /**
     * Registered Quantity
     * 已登记数量
     */
    registered_quantity?: number;
    /**
     * Available Quantity
     * 可登记数量
     */
    available_quantity: number;
    /**
     * Supports Order Level
     * 支持整单级别登记
     */
    supports_order_level?: boolean;
    /**
     * Supports Part Level
     * 支持部位级别登记
     */
    supports_part_level?: boolean;
    /**
     * Supports Bundle Level
     * 支持扎级别登记
     */
    supports_bundle_level?: boolean;
};

/**
 * AvailableFactoriesDTO
 * DTO for list of available factories.
 */
export type AvailableFactoriesDto = {
    /**
     * Factories
     */
    factories: Array<AvailableFactoryDto>;
    /**
     * Current Factory Id
     */
    current_factory_id?: number | null;
};

/**
 * AvailableFactoryDTO
 * DTO for available factory in switch list.
 */
export type AvailableFactoryDto = {
    /**
     * Factory Id
     */
    factory_id: number;
    /**
     * Factory Name
     */
    factory_name: string;
    /**
     * Factory Code
     */
    factory_code: string;
    /**
     * Department Id
     */
    department_id?: number | null;
    /**
     * Department Name
     */
    department_name?: string | null;
    /**
     * Role
     */
    role: string;
    /**
     * Employee Id
     */
    employee_id?: string | null;
    /**
     * Position
     */
    position?: string | null;
    /**
     * Is Manager
     */
    is_manager: boolean;
    /**
     * Is Current
     */
    is_current: boolean;
};

/**
 * AvailablePartDTO
 * 可登记的订单部位信息
 */
export type AvailablePartDto = {
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Total Quantity
     * 部位总数量
     */
    total_quantity: number;
    /**
     * Registered Quantity
     * 已登记数量
     */
    registered_quantity?: number;
    /**
     * Available Quantity
     * 可登记数量
     */
    available_quantity: number;
    /**
     * Order Bundles
     * 该部位下的可登记扎列表
     */
    order_bundles?: Array<AvailableBundleDto>;
};

/**
 * AvailableRegistrationDTO
 * 订单可登记数据
 */
export type AvailableRegistrationDto = {
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Status
     * 订单状态
     */
    order_status: string;
    /**
     * Total Quantity
     * 订单总数量
     */
    total_quantity: number;
    /**
     * Registered Quantity
     * 已登记数量
     */
    registered_quantity?: number;
    /**
     * Available Quantity
     * 可登记数量
     */
    available_quantity: number;
    /**
     * Craft Routes
     * 可登记的工艺路线列表
     */
    craft_routes?: Array<AvailableCraftRouteDto>;
    /**
     * Order Parts
     * 可登记的订单部位列表
     */
    order_parts?: Array<AvailablePartDto>;
};

/**
 * AvailableUsersDTO
 * DTO for available users (not in factory).
 */
export type AvailableUsersDto = {
    /**
     * Users
     * List of available users
     */
    users: Array<UserSummaryDto>;
    /**
     * Total
     * Total number of available users
     */
    total: number;
};

/**
 * BillApprovalRequestDTO
 * DTO for approving a bill.
 */
export type BillApprovalRequestDto = {
    /**
     * Review Notes
     * Review notes
     */
    review_notes?: string | null;
};

/**
 * BillCancellationRequestDTO
 * DTO for cancelling a bill.
 */
export type BillCancellationRequestDto = {
    /**
     * Cancellation Reason
     * Cancellation reason
     */
    cancellation_reason: string;
};

/**
 * BillListResponseDTO
 * DTO for bill list response.
 */
export type BillListResponseDto = {
    /**
     * Bills
     */
    bills: Array<BillResponseDto>;
    /**
     * Total
     */
    total: number;
    /**
     * Skip
     */
    skip: number;
    /**
     * Limit
     */
    limit: number;
};

/**
 * BillOperationResponseDTO
 * DTO for bill operation response.
 */
export type BillOperationResponseDto = {
    /**
     * Success
     */
    success: boolean;
    /**
     * Message
     */
    message: string;
    bill?: BillResponseDto | null;
};

/**
 * BillPaymentRequestDTO
 * DTO for marking a bill as paid.
 */
export type BillPaymentRequestDto = {
    /**
     * Payment method
     */
    payment_method: PaymentMethod;
    /**
     * Payment Reference
     * Payment reference
     */
    payment_reference?: string | null;
    /**
     * Payment Notes
     * Payment notes
     */
    payment_notes?: string | null;
};

/**
 * BillRejectionRequestDTO
 * DTO for rejecting a bill.
 */
export type BillRejectionRequestDto = {
    /**
     * Rejection Reason
     * Rejection reason
     */
    rejection_reason: string;
};

/**
 * BillResponseDTO
 * DTO for bill response.
 */
export type BillResponseDto = {
    /**
     * Id
     */
    id: number;
    /**
     * Factory Id
     */
    factory_id: number;
    /**
     * Worker User Id
     */
    worker_user_id: number;
    /**
     * Bill Date
     */
    bill_date: string;
    /**
     * Bill No
     */
    bill_no: string;
    /**
     * Total Completed Quantity
     */
    total_completed_quantity: number;
    /**
     * Total Work Instances
     */
    total_work_instances: number;
    /**
     * Total Work Duration Minutes
     */
    total_work_duration_minutes: number;
    /**
     * Base Amount
     */
    base_amount: string;
    /**
     * Bonus Amount
     */
    bonus_amount: string;
    /**
     * Deduction Amount
     */
    deduction_amount: string;
    /**
     * Total Amount
     */
    total_amount: string;
    status: BillStatus;
    /**
     * Reviewed By User Id
     */
    reviewed_by_user_id?: number | null;
    /**
     * Reviewed At
     */
    reviewed_at?: string | null;
    /**
     * Review Notes
     */
    review_notes?: string | null;
    payment_method?: PaymentMethod | null;
    /**
     * Paid By User Id
     */
    paid_by_user_id?: number | null;
    /**
     * Paid At
     */
    paid_at?: string | null;
    /**
     * Payment Reference
     */
    payment_reference?: string | null;
    /**
     * Payment Notes
     */
    payment_notes?: string | null;
    /**
     * Quality Score Average
     */
    quality_score_average?: string | null;
    /**
     * Defect Count
     */
    defect_count: number;
    /**
     * Rework Count
     */
    rework_count: number;
    /**
     * Breakdown By Granularity
     */
    breakdown_by_granularity?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Craft Route
     */
    breakdown_by_craft_route?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Order
     */
    breakdown_by_order?: {
        [key: string]: unknown;
    } | null;
    /**
     * Is Auto Generated
     */
    is_auto_generated: boolean;
    /**
     * Generated At
     */
    generated_at?: string | null;
    /**
     * Notes
     */
    notes?: string | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
    worker_user?: UserInfoDto | null;
    factory?: FactoryInfoDto | null;
    reviewed_by_user?: UserInfoDto | null;
    paid_by_user?: UserInfoDto | null;
};

/**
 * BillSettlementSummaryDTO
 * DTO for bill settlement summary.
 */
export type BillSettlementSummaryDto = {
    bills: BillSummaryDto;
    instances: InstanceSummaryDto;
};

/**
 * BillStatus
 * 账单状态枚举
 */
export type BillStatus = 'draft' | 'pending' | 'approved' | 'paid' | 'rejected' | 'cancelled';

/**
 * BillSubmitForReviewRequestDTO
 * DTO for submitting a bill for review.
 */
export type BillSubmitForReviewRequestDto = {
    [key: string]: unknown;
};

/**
 * BillSummaryDTO
 */
export type BillSummaryDto = {
    /**
     * Pending Count
     */
    pending_count: number;
    /**
     * Approved Count
     */
    approved_count: number;
    /**
     * Paid Count
     */
    paid_count: number;
    /**
     * Pending Amount
     */
    pending_amount: string;
    /**
     * Approved Amount
     */
    approved_amount: string;
    /**
     * Paid Amount
     */
    paid_amount: string;
};

/**
 * BindUserRoleDTO
 * DTO for binding system role to user.
 */
export type BindUserRoleDto = {
    /**
     * User Id
     * User ID
     */
    user_id: number;
    /**
     * Role Id
     * Role ID to assign
     */
    role_id: number;
};

/**
 * Body_login_api_v1_auth_token_post
 */
export type BodyLoginApiV1AuthTokenPost = {
    /**
     * Grant Type
     */
    grant_type?: string | null;
    /**
     * Username
     */
    username: string;
    /**
     * Password
     */
    password: string;
    /**
     * Scope
     */
    scope?: string;
    /**
     * Client Id
     */
    client_id?: string | null;
    /**
     * Client Secret
     */
    client_secret?: string | null;
};

/**
 * BulkCraftRouteCreateDTO
 * DTO for creating multiple craft routes at once.
 */
export type BulkCraftRouteCreateDto = {
    /**
     * Craft Code
     * Craft code for all routes
     */
    craft_code: string;
    /**
     * Routes
     * List of routes to create
     */
    routes: Array<CraftRouteCreateDto>;
};

/**
 * BulkCraftRouteOperationResultDTO
 * DTO for bulk craft route operation results.
 */
export type BulkCraftRouteOperationResultDto = {
    /**
     * Success
     * Whether operation succeeded overall
     */
    success: boolean;
    /**
     * Message
     * Overall result message
     */
    message: string;
    /**
     * Results
     * Individual operation results
     */
    results: Array<CraftOperationResultDto>;
    /**
     * Successful Count
     * Number of successful operations
     */
    successful_count: number;
    /**
     * Failed Count
     * Number of failed operations
     */
    failed_count: number;
};

/**
 * BulkOrderBundleCreateDTO
 * DTO for creating multiple order bundles.
 */
export type BulkOrderBundleCreateDto = {
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Order Bundles
     * 订单扎列表
     */
    order_bundles: Array<OrderBundleCreateDto>;
};

/**
 * BulkOrderLineCreateDTO
 * DTO for creating multiple order lines.
 */
export type BulkOrderLineCreateDto = {
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Lines
     * 订单行列表
     */
    order_lines: Array<OrderLineCreateDto>;
};

/**
 * BulkOrderPartCreateDTO
 * DTO for creating multiple order parts.
 */
export type BulkOrderPartCreateDto = {
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Parts
     * 订单部位列表
     */
    order_parts: Array<OrderPartCreateDto>;
};

/**
 * BundleStatusEnum
 * Bundle status enumeration for DTOs.
 */
export type BundleStatusEnum = 'planned' | 'cutting' | 'cut_completed' | 'sewing' | 'sew_completed' | 'quality_check' | 'completed' | 'rework' | 'on_hold' | 'cancelled';

/**
 * CertifySkillDTO
 * DTO for certifying user in skill.
 */
export type CertifySkillDto = {
    /**
     * User Factory Skill Id
     * User factory skill ID
     */
    user_factory_skill_id: number;
    /**
     * Certification Expires
     * Certification expiry date (ISO format)
     */
    certification_expires?: string | null;
    /**
     * Notes
     * Certification notes
     */
    notes?: string | null;
};

/**
 * CompletionGranularityDTO
 * 完成粒度枚举
 */
export type CompletionGranularityDto = 'bundle' | 'bed' | 'order';

/**
 * CraftCreateDTO
 * DTO for creating a new craft.
 */
export type CraftCreateDto = {
    /**
     * Code
     * Unique craft code
     */
    code: string;
    /**
     * Name
     * Craft name
     */
    name: string;
    /**
     * Priority
     * Craft priority (higher number = higher priority)
     */
    priority?: number;
    /**
     * Enabled
     * Whether the craft is enabled
     */
    enabled?: boolean;
    /**
     * Description
     * Craft description
     */
    description?: string | null;
};

/**
 * CraftInstanceCreateDTO
 * DTO for creating craft instances - worker completion records.
 */
export type CraftInstanceCreateDto = {
    /**
     * Order Craft Route Id
     * 订单工艺路线ID
     */
    order_craft_route_id: number;
    /**
     * 完成粒度
     */
    completion_granularity: CompletionGranularityDto;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part Nos
     * 订单部位号列表 (床级别或扎级别时填写)
     */
    order_part_nos?: Array<string> | null;
    /**
     * Order Bundle Nos
     * 订单扎号列表 (扎级别时填写)
     */
    order_bundle_nos?: Array<string> | null;
    /**
     * Order Part No
     * 订单部位号 (兼容性字段)
     */
    order_part_no?: string | null;
    /**
     * Order Bundle No
     * 订单扎号 (兼容性字段)
     */
    order_bundle_no?: string | null;
    /**
     * Worker User Id
     * 完成工人ID
     */
    worker_user_id: number;
    /**
     * Completed Quantity
     * 完成数量
     */
    completed_quantity: number;
    /**
     * Quality Level
     * 质量等级 A/B/C
     */
    quality_level?: string | null;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Qr Code Scanned
     * 扫描的二维码内容
     */
    qr_code_scanned?: string | null;
    /**
     * Scan Location
     * 扫码位置
     */
    scan_location?: string | null;
    /**
     * Device Info
     * 设备信息
     */
    device_info?: {
        [key: string]: unknown;
    } | null;
    /**
     * Measurement Data
     * 测量数据
     */
    measurement_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Registration Data
     * 登记数据
     */
    registration_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * CraftInstanceListDTO
 * DTO for craft instance list response.
 */
export type CraftInstanceListDto = {
    /**
     * Instances
     * 实例列表
     */
    instances: Array<CraftInstanceResponseDto>;
    /**
     * Total
     * 总数量
     */
    total: number;
    /**
     * Total Quantity
     * 总完成数量
     */
    total_quantity: number;
};

/**
 * CraftInstanceOperationResultDTO
 * DTO for craft instance operation results.
 */
export type CraftInstanceOperationResultDto = {
    /**
     * Success
     * 操作是否成功
     */
    success: boolean;
    /**
     * Message
     * 结果消息
     */
    message: string;
    /**
     * Instance Id
     * 实例ID
     */
    instance_id?: number | null;
    /**
     * Details
     * 详细信息
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * CraftInstanceQRScanDTO
 * DTO for QR code scanning registration.
 */
export type CraftInstanceQrScanDto = {
    /**
     * Qr Code Content
     * 二维码内容
     */
    qr_code_content: string;
    /**
     * Scan Location
     * 扫码位置
     */
    scan_location?: string | null;
    /**
     * Device Info
     * 设备信息
     */
    device_info?: {
        [key: string]: unknown;
    } | null;
    /**
     * Completed Quantity
     * 完成数量
     */
    completed_quantity: number;
    /**
     * Quality Level
     * 质量等级
     */
    quality_level?: string | null;
    /**
     * Measurement Data
     * 测量数据
     */
    measurement_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Registration Data
     * 登记数据
     */
    registration_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * CraftInstanceRejectionDTO
 * DTO for rejecting craft instances.
 */
export type CraftInstanceRejectionDto = {
    /**
     * Rejected By User Id
     * 拒绝人ID
     */
    rejected_by_user_id: number;
    /**
     * Reason
     * 拒绝原因
     */
    reason: string;
};

/**
 * CraftInstanceResponseDTO
 * DTO for craft instance response.
 */
export type CraftInstanceResponseDto = {
    /**
     * Id
     * 实例ID
     */
    id: number;
    /**
     * Factory Id
     * 工厂ID
     */
    factory_id: number;
    /**
     * Order Craft Route Id
     * 订单工艺路线ID
     */
    order_craft_route_id: number;
    /**
     * 完成粒度
     */
    completion_granularity: CompletionGranularityDto;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part Nos
     * 订单部位号列表
     */
    order_part_nos?: Array<string> | null;
    /**
     * Order Bundle Nos
     * 订单扎号列表
     */
    order_bundle_nos?: Array<string> | null;
    /**
     * Order Part No
     * 主要订单部位号
     */
    order_part_no?: string | null;
    /**
     * Order Bundle No
     * 主要订单扎号
     */
    order_bundle_no?: string | null;
    /**
     * Worker User Id
     * 完成工人ID
     */
    worker_user_id: number;
    /**
     * Worker Name
     * 工人姓名
     */
    worker_name?: string | null;
    /**
     * Completed Quantity
     * 完成数量
     */
    completed_quantity: number;
    /**
     * Quality Level
     * 质量等级
     */
    quality_level?: string | null;
    /**
     * Status
     * 状态
     */
    status: string;
    /**
     * 结算状态
     */
    settlement_status: SettlementStatusDto;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Completed At
     * 完成时间
     */
    completed_at: string;
    /**
     * Work Duration Minutes
     * 工作时长(分钟)
     */
    work_duration_minutes?: number | null;
    /**
     * Qr Code Scanned
     * 扫描的二维码内容
     */
    qr_code_scanned?: string | null;
    /**
     * Scan Location
     * 扫码位置
     */
    scan_location?: string | null;
    /**
     * Device Info
     * 设备信息
     */
    device_info?: {
        [key: string]: unknown;
    } | null;
    /**
     * Measurement Data
     * 测量数据
     */
    measurement_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Registration Data
     * 登记数据
     */
    registration_data?: {
        [key: string]: unknown;
    } | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
    /**
     * Craft Route Name
     * 工艺路线名称
     */
    craft_route_name?: string | null;
    /**
     * Skill Code
     * 技能代码
     */
    skill_code?: string | null;
    /**
     * Skill Name
     * 技能名称
     */
    skill_name?: string | null;
};

/**
 * CraftInstanceStatisticsDTO
 * DTO for craft instance statistics.
 */
export type CraftInstanceStatisticsDto = {
    /**
     * Total Instances
     * 总实例数
     */
    total_instances: number;
    /**
     * Total Quantity
     * 总完成数量
     */
    total_quantity: number;
    /**
     * Total Workers
     * 参与工人数
     */
    total_workers: number;
    /**
     * Average Quality Score
     * 平均质量分数
     */
    average_quality_score?: number | null;
    /**
     * Status Breakdown
     * 状态分解
     */
    status_breakdown: {
        [key: string]: number;
    };
    /**
     * Settlement Breakdown
     * 结算状态分解
     */
    settlement_breakdown: {
        [key: string]: number;
    };
    /**
     * Granularity Breakdown
     * 粒度分解
     */
    granularity_breakdown: {
        [key: string]: number;
    };
    /**
     * Quality Breakdown
     * 质量等级分解
     */
    quality_breakdown: {
        [key: string]: number;
    };
    /**
     * Daily Completion Trend
     * 每日完成趋势
     */
    daily_completion_trend: Array<{
        [key: string]: unknown;
    }>;
};

/**
 * CraftInstanceVerificationDTO
 * DTO for verifying craft instances.
 */
export type CraftInstanceVerificationDto = {
    /**
     * Verified By User Id
     * 验证人ID
     */
    verified_by_user_id: number;
    /**
     * Notes
     * 验证备注
     */
    notes?: string | null;
};

/**
 * CraftListDTO
 * DTO for craft list response.
 */
export type CraftListDto = {
    /**
     * Crafts
     * List of crafts
     */
    crafts: Array<CraftResponseDto>;
    /**
     * Total
     * Total number of crafts
     */
    total: number;
};

/**
 * CraftOperationResultDTO
 * DTO for craft operation results.
 */
export type CraftOperationResultDto = {
    /**
     * Success
     * Whether operation succeeded
     */
    success: boolean;
    /**
     * Message
     * Result message
     */
    message: string;
    /**
     * Craft Id
     * Craft ID affected
     */
    craft_id?: number | null;
    /**
     * Craft Route Id
     * Craft route ID affected
     */
    craft_route_id?: number | null;
    /**
     * Details
     * Additional operation details
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * CraftResponseDTO
 * DTO for craft response.
 */
export type CraftResponseDto = {
    /**
     * Code
     * Unique craft code
     */
    code: string;
    /**
     * Name
     * Craft name
     */
    name: string;
    /**
     * Priority
     * Craft priority (higher number = higher priority)
     */
    priority?: number;
    /**
     * Enabled
     * Whether the craft is enabled
     */
    enabled?: boolean;
    /**
     * Description
     * Craft description
     */
    description?: string | null;
    /**
     * Id
     * Craft ID
     */
    id: number;
    /**
     * Created At
     * Creation timestamp
     */
    created_at: string;
    /**
     * Updated At
     * Last update timestamp
     */
    updated_at: string;
};

/**
 * CraftRouteCreateDTO
 * DTO for creating a new craft route.
 */
export type CraftRouteCreateDto = {
    /**
     * Craft Code
     * Craft code
     */
    craft_code: string;
    /**
     * Skill Code
     * Skill code
     */
    skill_code: string;
    /**
     * Code
     * Unique code within craft
     */
    code: string;
    /**
     * Name
     * Display name for this route
     */
    name: string;
    /**
     * Order
     * Order in the craft workflow
     */
    order?: number;
    /**
     * Measurement Types
     * Available measurement types
     */
    measurement_types?: Array<string> | null;
    /**
     * Registration Types
     * Available registration types
     */
    registration_types?: Array<string> | null;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
    /**
     * Is Required
     * Whether this step is required
     */
    is_required?: boolean;
};

/**
 * CraftRouteDetailDTO
 * DTO for detailed craft route with skill information.
 */
export type CraftRouteDetailDto = {
    /**
     * Id
     * Craft route ID
     */
    id: number;
    /**
     * Craft Code
     * Craft code
     */
    craft_code: string;
    /**
     * Skill Code
     * Skill code
     */
    skill_code: string;
    /**
     * Skill Name
     * Skill name
     */
    skill_name: string;
    /**
     * Code
     * Unique code within craft
     */
    code: string;
    /**
     * Name
     * Display name for this route
     */
    name: string;
    /**
     * Order
     * Order in the craft workflow
     */
    order: number;
    /**
     * Measurement Types
     * Available measurement types
     */
    measurement_types: Array<string>;
    /**
     * Registration Types
     * Available registration types
     */
    registration_types: Array<string>;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
    /**
     * Is Required
     * Whether this step is required
     */
    is_required: boolean;
    /**
     * Created At
     * Creation timestamp
     */
    created_at: string;
    /**
     * Updated At
     * Last update timestamp
     */
    updated_at: string;
};

/**
 * CraftRouteListDTO
 * DTO for craft route list response.
 */
export type CraftRouteListDto = {
    /**
     * Routes
     * List of craft routes
     */
    routes: Array<CraftRouteDetailDto>;
    /**
     * Total
     * Total number of routes
     */
    total: number;
};

/**
 * CraftRouteResponseDTO
 * DTO for craft route response.
 */
export type CraftRouteResponseDto = {
    /**
     * Craft Code
     * Craft code
     */
    craft_code: string;
    /**
     * Skill Code
     * Skill code
     */
    skill_code: string;
    /**
     * Code
     * Unique code within craft
     */
    code: string;
    /**
     * Name
     * Display name for this route
     */
    name: string;
    /**
     * Order
     * Order in the craft workflow
     */
    order?: number;
    /**
     * Measurement Types
     * Available measurement types
     */
    measurement_types?: Array<string> | null;
    /**
     * Registration Types
     * Available registration types
     */
    registration_types?: Array<string> | null;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
    /**
     * Is Required
     * Whether this step is required
     */
    is_required?: boolean;
    /**
     * Id
     * Craft route ID
     */
    id: number;
    /**
     * Created At
     * Creation timestamp
     */
    created_at: string;
    /**
     * Updated At
     * Last update timestamp
     */
    updated_at: string;
};

/**
 * CraftRouteUpdateDTO
 * DTO for updating a craft route.
 */
export type CraftRouteUpdateDto = {
    /**
     * Code
     * Unique code within craft
     */
    code?: string | null;
    /**
     * Name
     * Display name for this route
     */
    name?: string | null;
    /**
     * Order
     * Order in the craft workflow
     */
    order?: number | null;
    /**
     * Measurement Types
     * Available measurement types
     */
    measurement_types?: Array<string> | null;
    /**
     * Registration Types
     * Available registration types
     */
    registration_types?: Array<string> | null;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
    /**
     * Is Required
     * Whether this step is required
     */
    is_required?: boolean | null;
};

/**
 * CraftUpdateDTO
 * DTO for updating a craft.
 */
export type CraftUpdateDto = {
    /**
     * Name
     * Craft name
     */
    name?: string | null;
    /**
     * Priority
     * Craft priority
     */
    priority?: number | null;
    /**
     * Enabled
     * Whether the craft is enabled
     */
    enabled?: boolean | null;
    /**
     * Description
     * Craft description
     */
    description?: string | null;
};

/**
 * CraftWithRoutesDTO
 * DTO for craft with its routes.
 */
export type CraftWithRoutesDto = {
    /**
     * Code
     * Unique craft code
     */
    code: string;
    /**
     * Name
     * Craft name
     */
    name: string;
    /**
     * Priority
     * Craft priority (higher number = higher priority)
     */
    priority?: number;
    /**
     * Enabled
     * Whether the craft is enabled
     */
    enabled?: boolean;
    /**
     * Description
     * Craft description
     */
    description?: string | null;
    /**
     * Id
     * Craft ID
     */
    id: number;
    /**
     * Created At
     * Creation timestamp
     */
    created_at: string;
    /**
     * Updated At
     * Last update timestamp
     */
    updated_at: string;
    /**
     * Routes
     * Craft routes ordered by order field
     */
    routes: Array<CraftRouteDetailDto>;
};

/**
 * CreateBillRequestDTO
 * DTO for creating a new bill.
 */
export type CreateBillRequestDto = {
    /**
     * Factory Id
     * Factory ID
     */
    factory_id: number;
    /**
     * Worker User Id
     * Worker user ID
     */
    worker_user_id: number;
    /**
     * Bill Date
     * Bill date
     */
    bill_date: string;
    /**
     * Total Completed Quantity
     * Total completed quantity
     */
    total_completed_quantity?: number;
    /**
     * Total Work Instances
     * Total work instances
     */
    total_work_instances?: number;
    /**
     * Total Work Duration Minutes
     * Total work duration in minutes
     */
    total_work_duration_minutes?: number;
    /**
     * Base Amount
     * Base amount
     */
    base_amount?: number | string;
    /**
     * Bonus Amount
     * Bonus amount
     */
    bonus_amount?: number | string;
    /**
     * Deduction Amount
     * Deduction amount
     */
    deduction_amount?: number | string;
    /**
     * Total Amount
     * Total amount
     */
    total_amount?: number | string;
    /**
     * Quality Score Average
     * Average quality score
     */
    quality_score_average?: number | string | null;
    /**
     * Defect Count
     * Defect count
     */
    defect_count?: number;
    /**
     * Rework Count
     * Rework count
     */
    rework_count?: number;
    /**
     * Breakdown By Granularity
     * Breakdown by granularity
     */
    breakdown_by_granularity?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Craft Route
     * Breakdown by craft route
     */
    breakdown_by_craft_route?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Order
     * Breakdown by order
     */
    breakdown_by_order?: {
        [key: string]: unknown;
    } | null;
    /**
     * Notes
     * Notes
     */
    notes?: string | null;
};

/**
 * CreateUserWithFactoryDTO
 * DTO for creating a new user with factory relationship and skills.
 */
export type CreateUserWithFactoryDto = {
    /**
     * Username
     * Username
     */
    username: string;
    /**
     * Email
     * Email address
     */
    email: string;
    /**
     * Password
     * Password
     */
    password: string;
    /**
     * Full Name
     * Full name
     */
    full_name?: string | null;
    /**
     * Phone
     * Phone number (Chinese format)
     */
    phone?: string | null;
    /**
     * Is Active
     * Whether user is active
     */
    is_active?: boolean;
    /**
     * Role Id
     * System role ID to assign
     */
    role_id?: number | null;
    /**
     * Factory Role
     * Role in factory (WORKER, SUPERVISOR, MANAGER, ADMIN)
     */
    factory_role?: string;
    /**
     * Department Id
     * Department ID in factory
     */
    department_id?: number | null;
    /**
     * Start Date
     * Start date (ISO format)
     */
    start_date?: string | null;
    /**
     * Position
     * Position/job title
     */
    position?: string | null;
    /**
     * Employee Id
     * Employee ID
     */
    employee_id?: string | null;
    /**
     * Skills
     * Initial skills to assign
     */
    skills?: Array<UserSkillCreateDto>;
};

/**
 * DepartmentCreateDTO
 * DTO for creating a new department.
 */
export type DepartmentCreateDto = {
    /**
     * Name
     * Department name
     */
    name: string;
    /**
     * Code
     * Department code
     */
    code: string;
    /**
     * Manager Name
     * Manager name
     */
    manager_name?: string | null;
    /**
     * Phone
     * Phone number
     */
    phone?: string | null;
    /**
     * Email
     * Email address
     */
    email?: string | null;
    /**
     * Location
     * Department location
     */
    location?: string | null;
    /**
     * Description
     * Department description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the department is active
     */
    is_active?: boolean;
    /**
     * Operator Id
     * ID of the user creating the department
     */
    operator_id: number;
};

/**
 * DepartmentListDTO
 * DTO for department list response.
 */
export type DepartmentListDto = {
    /**
     * Departments
     * List of departments
     */
    departments: Array<DepartmentResponseDto>;
    /**
     * Total
     * Total number of departments
     */
    total: number;
};

/**
 * DepartmentOperationResultDTO
 * DTO for department operation results.
 */
export type DepartmentOperationResultDto = {
    /**
     * Success
     * Whether the operation was successful
     */
    success: boolean;
    /**
     * Message
     * Operation result message
     */
    message: string;
    /**
     * Department Id
     * Department ID if applicable
     */
    department_id?: number | null;
    /**
     * Operator Id
     * ID of the user who performed the operation
     */
    operator_id: number;
};

/**
 * DepartmentResponseDTO
 * DTO for department response.
 */
export type DepartmentResponseDto = {
    /**
     * Name
     * Department name
     */
    name: string;
    /**
     * Code
     * Department code
     */
    code: string;
    /**
     * Manager Name
     * Manager name
     */
    manager_name?: string | null;
    /**
     * Phone
     * Phone number
     */
    phone?: string | null;
    /**
     * Email
     * Email address
     */
    email?: string | null;
    /**
     * Location
     * Department location
     */
    location?: string | null;
    /**
     * Description
     * Department description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the department is active
     */
    is_active?: boolean;
    /**
     * Id
     * Department ID
     */
    id: number;
    /**
     * Factory Id
     * Factory ID
     */
    factory_id: number;
    /**
     * Created At
     * Creation timestamp
     */
    created_at: string;
    /**
     * Updated At
     * Last update timestamp
     */
    updated_at: string;
};

/**
 * DepartmentUpdateDTO
 * DTO for updating a department.
 */
export type DepartmentUpdateDto = {
    /**
     * Name
     * Department name
     */
    name?: string | null;
    /**
     * Code
     * Department code
     */
    code?: string | null;
    /**
     * Manager Name
     * Manager name
     */
    manager_name?: string | null;
    /**
     * Phone
     * Phone number
     */
    phone?: string | null;
    /**
     * Email
     * Email address
     */
    email?: string | null;
    /**
     * Location
     * Department location
     */
    location?: string | null;
    /**
     * Description
     * Department description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the department is active
     */
    is_active?: boolean | null;
    /**
     * Operator Id
     * ID of the user updating the department
     */
    operator_id: number;
};

/**
 * FactoryContextDTO
 * Factory context information for user session.
 */
export type FactoryContextDto = {
    /**
     * Factory Id
     */
    factory_id?: number | null;
    /**
     * Factory Name
     */
    factory_name?: string | null;
    /**
     * Department Id
     */
    department_id?: number | null;
    /**
     * Role
     */
    role?: string | null;
    /**
     * Is Manager
     */
    is_manager?: boolean;
};

/**
 * FactoryInfoDTO
 * DTO for factory information in bill responses.
 */
export type FactoryInfoDto = {
    /**
     * Id
     */
    id: number;
    /**
     * Name
     */
    name: string;
    /**
     * Code
     */
    code: string;
};

/**
 * FactoryJoinApprovalDTO
 * DTO for approving/rejecting factory join requests.
 */
export type FactoryJoinApprovalDto = {
    /**
     * User Factory Id
     * UserFactory record ID
     */
    user_factory_id: number;
    /**
     * Action
     * approve or reject
     */
    action: string;
    /**
     * Employee Id
     * Employee ID (if approved)
     */
    employee_id?: string | null;
    /**
     * Position
     * Position title (if approved)
     */
    position?: string | null;
    /**
     * Department Id
     * Department assignment (if approved)
     */
    department_id?: number | null;
    /**
     * User role in factory
     */
    role?: UserFactoryRoleEnum | null;
    /**
     * Reason
     * Rejection reason (if rejected)
     */
    reason?: string | null;
    /**
     * Start Date
     * Start date (if approved)
     */
    start_date?: string | null;
};

/**
 * FactoryJoinRequestDTO
 * DTO for requesting to join a factory.
 */
export type FactoryJoinRequestDto = {
    /**
     * Factory Id
     * Factory ID to join
     */
    factory_id: number;
    /**
     * Department Id
     * Optional department ID
     */
    department_id?: number | null;
    /**
     * Message
     * Optional message to manager
     */
    message?: string | null;
};

/**
 * FactoryUserDTO
 * DTO for factory user with factory-specific information.
 */
export type FactoryUserDto = {
    /**
     * User information
     */
    user: UserSummaryDto;
    /**
     * Factory Role
     * Role in factory (WORKER, SUPERVISOR, MANAGER, ADMIN)
     */
    factory_role: string;
    /**
     * Factory Status
     * Status in factory (APPROVED, SUSPENDED, etc.)
     */
    factory_status: string;
    /**
     * Joined At
     * Date joined factory
     */
    joined_at?: string | null;
    /**
     * Skills
     * User's skills in this factory
     */
    skills?: Array<UserFactorySkillDto>;
    /**
     * Skills Count
     * Total number of skills
     */
    skills_count?: number;
    /**
     * Certified Skills Count
     * Number of certified skills
     */
    certified_skills_count?: number;
};

/**
 * FactoryUserListDTO
 * DTO for factory user list response.
 */
export type FactoryUserListDto = {
    /**
     * Users
     * List of factory users
     */
    users: Array<FactoryUserDto>;
    /**
     * Total
     * Total number of users
     */
    total: number;
    /**
     * Factory Id
     * Factory ID
     */
    factory_id: number;
    /**
     * Factory Name
     * Factory name
     */
    factory_name: string;
};

/**
 * GenerateImageCodeDTO
 * DTO for generating image validation code.
 */
export type GenerateImageCodeDto = {
    /**
     * Session Id
     * Optional session ID, will generate if not provided
     */
    session_id?: string | null;
};

/**
 * HTTPValidationError
 */
export type HttpValidationError = {
    /**
     * Detail
     */
    detail?: Array<ValidationError>;
};

/**
 * ImageCodeResponseDTO
 * Response DTO for image validation code.
 */
export type ImageCodeResponseDto = {
    /**
     * Session Id
     */
    session_id: string;
    /**
     * Image Base64
     */
    image_base64: string;
    /**
     * Expires In Seconds
     */
    expires_in_seconds: number;
};

/**
 * InstanceDisputeRequestDTO
 * DTO for disputing a bill instance.
 */
export type InstanceDisputeRequestDto = {
    /**
     * Dispute Reason
     * Dispute reason
     */
    dispute_reason: string;
};

/**
 * InstanceDisputeResolutionRequestDTO
 * DTO for resolving an instance dispute.
 */
export type InstanceDisputeResolutionRequestDto = {
    /**
     * Resolution Notes
     * Resolution notes
     */
    resolution_notes: string;
    /**
     * Include In Settlement
     * Whether to include in settlement
     */
    include_in_settlement: boolean;
};

/**
 * InstanceSummaryDTO
 */
export type InstanceSummaryDto = {
    /**
     * Pending Count
     */
    pending_count: number;
    /**
     * Included Count
     */
    included_count: number;
    /**
     * Settled Count
     */
    settled_count: number;
    /**
     * Disputed Count
     */
    disputed_count: number;
};

/**
 * MyFactoriesDTO
 * DTO for current user's factories.
 */
export type MyFactoriesDto = {
    /**
     * Active Factories
     */
    active_factories: Array<UserFactoryResponseDto>;
    /**
     * Pending Requests
     */
    pending_requests: Array<UserFactoryResponseDto>;
    /**
     * Managed Factories
     */
    managed_factories: Array<UserFactoryResponseDto>;
};

/**
 * OrderAmountUpdateDTO
 * DTO for updating order total amount.
 */
export type OrderAmountUpdateDto = {
    /**
     * Total Amount
     * 新的总数量
     */
    total_amount: number;
    /**
     * Notes
     * 变更备注
     */
    notes?: string | null;
};

/**
 * OrderBundleCreateDTO
 * DTO for creating a new order bundle.
 */
export type OrderBundleCreateDto = {
    /**
     * Size
     * 尺码
     */
    size: string;
    /**
     * Quantity
     * 扎件数
     */
    quantity: number;
    /**
     * Color
     * 颜色
     */
    color: string;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Cutting Machine
     * 裁剪机床
     */
    cutting_machine?: string | null;
    /**
     * Sewing Machine
     * 缝制机床
     */
    sewing_machine?: string | null;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Bundle Sequence
     * 扎序号(同一部位、同一尺码内)
     */
    bundle_sequence: number;
    /**
     * Cutter User Id
     * 裁剪工ID
     */
    cutter_user_id?: number | null;
    /**
     * Sewer User Id
     * 缝制工ID
     */
    sewer_user_id?: number | null;
    /**
     * Qc User Id
     * 质检员ID
     */
    qc_user_id?: number | null;
};

/**
 * OrderBundleListDTO
 * DTO for order bundle list response.
 */
export type OrderBundleListDto = {
    /**
     * Order Bundles
     * 订单扎列表
     */
    order_bundles: Array<OrderBundleResponseDto>;
    /**
     * Total
     * 总数量
     */
    total: number;
};

/**
 * OrderBundleOperationResultDTO
 * DTO for order bundle operation results.
 */
export type OrderBundleOperationResultDto = {
    /**
     * Success
     * 操作是否成功
     */
    success: boolean;
    /**
     * Message
     * 结果消息
     */
    message: string;
    /**
     * Order Bundle Id
     * 订单扎ID
     */
    order_bundle_id?: number | null;
    /**
     * Order Bundle No
     * 订单扎号
     */
    order_bundle_no?: string | null;
    /**
     * Details
     * 详细信息
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * OrderBundleProductionUpdateDTO
 * DTO for updating order bundle production.
 */
export type OrderBundleProductionUpdateDto = {
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity?: number | null;
    /**
     * Defective Quantity
     * 次品件数
     */
    defective_quantity?: number | null;
    /**
     * Rework Quantity
     * 返工件数
     */
    rework_quantity?: number | null;
    /**
     * Quality Level
     * 质量等级
     */
    quality_level?: string | null;
    /**
     * Quality Notes
     * 质量备注
     */
    quality_notes?: string | null;
    /**
     * Notes
     * 生产备注
     */
    notes?: string | null;
};

/**
 * OrderBundleResponseDTO
 * DTO for order bundle response.
 */
export type OrderBundleResponseDto = {
    /**
     * Size
     * 尺码
     */
    size: string;
    /**
     * Quantity
     * 扎件数
     */
    quantity: number;
    /**
     * Color
     * 颜色
     */
    color: string;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Cutting Machine
     * 裁剪机床
     */
    cutting_machine?: string | null;
    /**
     * Sewing Machine
     * 缝制机床
     */
    sewing_machine?: string | null;
    /**
     * Id
     * 订单扎ID
     */
    id: number;
    /**
     * Factory Id
     * 工厂ID
     */
    factory_id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Bundle No
     * 订单扎号
     */
    order_bundle_no: string;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Bundle Sequence
     * 扎序号
     */
    bundle_sequence: number;
    /**
     * 扎状态
     */
    status: BundleStatusEnum;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity: number;
    /**
     * Defective Quantity
     * 次品件数
     */
    defective_quantity: number;
    /**
     * Rework Quantity
     * 返工件数
     */
    rework_quantity: number;
    /**
     * Progress Percentage
     * 完成百分比
     */
    progress_percentage: number;
    /**
     * Actual Start Date
     * 实际开始时间
     */
    actual_start_date?: string | null;
    /**
     * Actual End Date
     * 实际完成时间
     */
    actual_end_date?: string | null;
    /**
     * Cut Start Date
     * 裁剪开始时间
     */
    cut_start_date?: string | null;
    /**
     * Cut End Date
     * 裁剪完成时间
     */
    cut_end_date?: string | null;
    /**
     * Sew Start Date
     * 缝制开始时间
     */
    sew_start_date?: string | null;
    /**
     * Sew End Date
     * 缝制完成时间
     */
    sew_end_date?: string | null;
    /**
     * Qc Start Date
     * 质检开始时间
     */
    qc_start_date?: string | null;
    /**
     * Qc End Date
     * 质检完成时间
     */
    qc_end_date?: string | null;
    /**
     * Cutter User Id
     * 裁剪工ID
     */
    cutter_user_id?: number | null;
    /**
     * Sewer User Id
     * 缝制工ID
     */
    sewer_user_id?: number | null;
    /**
     * Qc User Id
     * 质检员ID
     */
    qc_user_id?: number | null;
    /**
     * Cutter Name
     * 裁剪工姓名
     */
    cutter_name?: string | null;
    /**
     * Sewer Name
     * 缝制工姓名
     */
    sewer_name?: string | null;
    /**
     * Qc User Name
     * 质检员姓名
     */
    qc_user_name?: string | null;
    /**
     * Quality Level
     * 质量等级(A/B/C)
     */
    quality_level?: string | null;
    /**
     * Quality Notes
     * 质量备注
     */
    quality_notes?: string | null;
    /**
     * Good Quantity
     * 良品数量
     */
    good_quantity?: number | null;
    /**
     * Processing Time
     * 总加工时长(分钟)
     */
    processing_time?: number | null;
    /**
     * Cutting Time
     * 裁剪时长(分钟)
     */
    cutting_time?: number | null;
    /**
     * Sewing Time
     * 缝制时长(分钟)
     */
    sewing_time?: number | null;
    /**
     * Qc Time
     * 质检时长(分钟)
     */
    qc_time?: number | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
};

/**
 * OrderBundleStatisticsDTO
 * DTO for order bundle statistics.
 */
export type OrderBundleStatisticsDto = {
    /**
     * Total Order Bundles
     * 总订单扎数
     */
    total_order_bundles: number;
    /**
     * Total Quantity
     * 总件数
     */
    total_quantity: number;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity: number;
    /**
     * Defective Quantity
     * 次品件数
     */
    defective_quantity: number;
    /**
     * Rework Quantity
     * 返工件数
     */
    rework_quantity: number;
    /**
     * Good Quantity
     * 良品件数
     */
    good_quantity: number;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage: number;
    /**
     * Quality Rate
     * 质量合格率
     */
    quality_rate: number;
    /**
     * Status Breakdown
     * 状态分解
     */
    status_breakdown: {
        [key: string]: unknown;
    };
    /**
     * Planned Bundles
     * 计划中扎数
     */
    planned_bundles: number;
    /**
     * Cutting Bundles
     * 裁剪中扎数
     */
    cutting_bundles: number;
    /**
     * Cut Completed Bundles
     * 裁剪完成扎数
     */
    cut_completed_bundles: number;
    /**
     * Sewing Bundles
     * 缝制中扎数
     */
    sewing_bundles: number;
    /**
     * Sew Completed Bundles
     * 缝制完成扎数
     */
    sew_completed_bundles: number;
    /**
     * Quality Check Bundles
     * 质检中扎数
     */
    quality_check_bundles: number;
    /**
     * Completed Bundles
     * 已完成扎数
     */
    completed_bundles: number;
    /**
     * Rework Bundles
     * 返工扎数
     */
    rework_bundles: number;
    /**
     * On Hold Bundles
     * 暂停扎数
     */
    on_hold_bundles: number;
    /**
     * Cancelled Bundles
     * 已取消扎数
     */
    cancelled_bundles: number;
};

/**
 * OrderBundleStatusUpdateDTO
 * DTO for updating order bundle status.
 */
export type OrderBundleStatusUpdateDto = {
    /**
     * 新状态
     */
    status: BundleStatusEnum;
    /**
     * Notes
     * 状态变更备注
     */
    notes?: string | null;
    /**
     * User Id
     * 操作用户ID
     */
    user_id?: number | null;
    /**
     * Machine
     * 机床编号
     */
    machine?: string | null;
};

/**
 * OrderBundleUpdateDTO
 * DTO for updating an order bundle.
 */
export type OrderBundleUpdateDto = {
    /**
     * Quantity
     * 扎件数
     */
    quantity?: number | null;
    /**
     * 扎状态
     */
    status?: BundleStatusEnum | null;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity?: number | null;
    /**
     * Defective Quantity
     * 次品件数
     */
    defective_quantity?: number | null;
    /**
     * Rework Quantity
     * 返工件数
     */
    rework_quantity?: number | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Cutting Machine
     * 裁剪机床
     */
    cutting_machine?: string | null;
    /**
     * Sewing Machine
     * 缝制机床
     */
    sewing_machine?: string | null;
    /**
     * Cutter User Id
     * 裁剪工ID
     */
    cutter_user_id?: number | null;
    /**
     * Sewer User Id
     * 缝制工ID
     */
    sewer_user_id?: number | null;
    /**
     * Qc User Id
     * 质检员ID
     */
    qc_user_id?: number | null;
    /**
     * Quality Level
     * 质量等级(A/B/C)
     */
    quality_level?: string | null;
    /**
     * Quality Notes
     * 质量备注
     */
    quality_notes?: string | null;
};

/**
 * OrderCraftCreateDTO
 * DTO for creating order crafts.
 */
export type OrderCraftCreateDto = {
    /**
     * Craft Code
     * 工艺代码
     */
    craft_code: string;
    /**
     * Craft Name
     * 工艺名称
     */
    craft_name?: string | null;
    /**
     * Order
     * 工艺顺序
     */
    order: number;
    /**
     * Is Required
     * 是否必需
     */
    is_required?: boolean;
    /**
     * Estimated Duration Hours
     * 预计耗时(小时)
     */
    estimated_duration_hours?: number | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Order Craft Routes
     * 工艺路线列表
     */
    order_craft_routes?: Array<OrderCraftRouteCreateDto>;
};

/**
 * OrderCraftOperationResultDTO
 * DTO for order craft operation results.
 */
export type OrderCraftOperationResultDto = {
    /**
     * Success
     * 操作是否成功
     */
    success: boolean;
    /**
     * Message
     * 结果消息
     */
    message: string;
    /**
     * Order Craft Id
     * 订单工艺ID
     */
    order_craft_id?: number | null;
    /**
     * Order Craft Route Id
     * 订单工艺路线ID
     */
    order_craft_route_id?: number | null;
    /**
     * Details
     * 详细信息
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * OrderCraftProgressDTO
 * DTO for updating order craft progress.
 */
export type OrderCraftProgressDto = {
    /**
     * Craft Code
     * 工艺代码
     */
    craft_code: string;
    /**
     * Craft Route Id
     * 工艺路线ID
     */
    craft_route_id?: number | null;
    /**
     * Notes
     * 进度备注
     */
    notes?: string | null;
};

/**
 * OrderCraftResponseDTO
 * DTO for order craft response.
 */
export type OrderCraftResponseDto = {
    /**
     * Id
     * ID
     */
    id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Craft Code
     * 工艺代码
     */
    craft_code: string;
    /**
     * Craft Name
     * 工艺名称
     */
    craft_name?: string | null;
    /**
     * Order
     * 工艺顺序
     */
    order: number;
    /**
     * Is Required
     * 是否必需
     */
    is_required: boolean;
    /**
     * Is Active
     * 是否活跃
     */
    is_active: boolean;
    /**
     * Status
     * 状态
     */
    status: string;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Completed At
     * 完成时间
     */
    completed_at?: string | null;
    /**
     * Estimated Duration Hours
     * 预计耗时(小时)
     */
    estimated_duration_hours?: number | null;
    /**
     * Actual Duration Hours
     * 实际耗时(小时)
     */
    actual_duration_hours?: number | null;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage?: number;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
    /**
     * Order Craft Routes
     * 工艺路线列表
     */
    order_craft_routes?: Array<OrderCraftRouteResponseDto>;
};

/**
 * OrderCraftRouteCreateDTO
 * DTO for creating order craft routes.
 */
export type OrderCraftRouteCreateDto = {
    /**
     * Skill Code
     * 技能代码
     */
    skill_code: string;
    /**
     * Name
     * 路线名称
     */
    name?: string | null;
    /**
     * Code
     * 路线代码
     */
    code?: string | null;
    /**
     * Order
     * 工序顺序
     */
    order: number;
    /**
     * Measurement Types
     * 测量类型列表
     */
    measurement_types?: Array<string> | null;
    /**
     * Registration Types
     * 登记类型列表
     */
    registration_types?: Array<string> | null;
    /**
     * Is Required
     * 是否必需
     */
    is_required?: boolean;
    /**
     * Estimated Duration Minutes
     * 预计耗时(分钟)
     */
    estimated_duration_minutes?: number | null;
    /**
     * Assigned User Id
     * 指定用户ID
     */
    assigned_user_id?: number | null;
    /**
     * Price
     * 单价
     */
    price?: number | string | null;
    /**
     * Total Cost
     * 总成本
     */
    total_cost?: number | string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * OrderCraftRouteResponseDTO
 * DTO for order craft route response.
 */
export type OrderCraftRouteResponseDto = {
    /**
     * Id
     * ID
     */
    id: number;
    /**
     * Order Craft Id
     * 订单工艺ID
     */
    order_craft_id: number;
    /**
     * Skill Code
     * 技能代码
     */
    skill_code: string;
    /**
     * Skill Name
     * 技能名称
     */
    skill_name?: string | null;
    /**
     * Name
     * 路线名称
     */
    name?: string | null;
    /**
     * Code
     * 路线代码
     */
    code?: string | null;
    /**
     * Order
     * 工序顺序
     */
    order: number;
    /**
     * Measurement Types
     * 测量类型列表
     */
    measurement_types?: Array<string> | null;
    /**
     * Registration Types
     * 登记类型列表
     */
    registration_types?: Array<string> | null;
    /**
     * Is Required
     * 是否必需
     */
    is_required: boolean;
    /**
     * Is Active
     * 是否活跃
     */
    is_active: boolean;
    /**
     * Status
     * 状态
     */
    status: string;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Completed At
     * 完成时间
     */
    completed_at?: string | null;
    /**
     * Assigned User Id
     * 指定用户ID
     */
    assigned_user_id?: number | null;
    /**
     * Assigned User Name
     * 指定用户姓名
     */
    assigned_user_name?: string | null;
    /**
     * Estimated Duration Minutes
     * 预计耗时(分钟)
     */
    estimated_duration_minutes?: number | null;
    /**
     * Actual Duration Minutes
     * 实际耗时(分钟)
     */
    actual_duration_minutes?: number | null;
    /**
     * Price
     * 单价
     */
    price?: string | null;
    /**
     * Total Cost
     * 总成本
     */
    total_cost?: string | null;
    /**
     * Quality Score
     * 质量分数
     */
    quality_score?: number | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Completion Notes
     * 完成备注
     */
    completion_notes?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
};

/**
 * OrderCraftRouteStatusUpdateDTO
 * DTO for updating order craft route status.
 */
export type OrderCraftRouteStatusUpdateDto = {
    /**
     * Status
     * 新状态 (pending, in_progress, completed, skipped)
     */
    status: string;
    /**
     * Quality Score
     * 质量分数
     */
    quality_score?: number | null;
    /**
     * Completion Notes
     * 完成备注
     */
    completion_notes?: string | null;
};

/**
 * OrderCraftStatisticsDTO
 * DTO for order craft statistics.
 */
export type OrderCraftStatisticsDto = {
    /**
     * Total Order Crafts
     * 总订单工艺数
     */
    total_order_crafts: number;
    /**
     * Pending Crafts
     * 待处理工艺数
     */
    pending_crafts: number;
    /**
     * In Progress Crafts
     * 进行中工艺数
     */
    in_progress_crafts: number;
    /**
     * Completed Crafts
     * 已完成工艺数
     */
    completed_crafts: number;
    /**
     * Skipped Crafts
     * 跳过工艺数
     */
    skipped_crafts: number;
    /**
     * Active Crafts
     * 活跃工艺数
     */
    active_crafts: number;
    /**
     * Inactive Crafts
     * 非活跃工艺数
     */
    inactive_crafts: number;
    /**
     * Status Breakdown
     * 状态分解
     */
    status_breakdown: {
        [key: string]: unknown;
    };
    /**
     * Active Breakdown
     * 活跃状态分解
     */
    active_breakdown: {
        [key: string]: unknown;
    };
};

/**
 * OrderCraftStatusUpdateDTO
 * DTO for updating order craft status.
 */
export type OrderCraftStatusUpdateDto = {
    /**
     * Status
     * 新状态 (pending, in_progress, completed, skipped)
     */
    status: string;
    /**
     * Notes
     * 状态变更备注
     */
    notes?: string | null;
};

/**
 * OrderCraftWorkflowDTO
 * DTO for order craft workflow configuration.
 */
export type OrderCraftWorkflowDto = {
    /**
     * Order Crafts
     * 订单工艺配置列表
     */
    order_crafts: Array<OrderCraftCreateDto>;
};

/**
 * OrderCreateDTO
 * DTO for creating a new order.
 */
export type OrderCreateDto = {
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * External Skc No
     * 外部款号
     */
    external_skc_no?: string | null;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * External Order No
     * 外部订单号
     */
    external_order_no?: string | null;
    /**
     * External Order No2
     * 外部订单号2
     */
    external_order_no2?: string | null;
    /**
     * Cost
     * 成本
     */
    cost?: number | string | null;
    /**
     * Price
     * 价格
     */
    price?: number | string | null;
    /**
     * Expect Finished At
     * 预期完成时间
     */
    expect_finished_at?: string | null;
    /**
     * Owner User Id
     * 负责人用户ID
     */
    owner_user_id?: number | null;
    /**
     * Description
     * 订单描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Order Lines
     * 订单行列表
     */
    order_lines: Array<OrderLineCreateDto>;
    /**
     * Order Crafts
     * 订单工艺配置列表
     */
    order_crafts?: Array<OrderCraftCreateDto> | null;
};

/**
 * OrderDashboardDTO
 * DTO for order dashboard data.
 */
export type OrderDashboardDto = {
    /**
     * 订单统计
     */
    order_statistics: OrderStatisticsDto;
    /**
     * Recent Orders
     * 最近订单
     */
    recent_orders: Array<OrderSummaryDto>;
    /**
     * Overdue Orders
     * 逾期订单
     */
    overdue_orders: Array<OrderSummaryDto>;
    /**
     * Orders By Craft
     * 按工艺分组的订单
     */
    orders_by_craft: {
        [key: string]: unknown;
    };
    /**
     * 生产汇总
     */
    production_summary: OrderLineStatisticsDto;
};

/**
 * OrderDetailResponseDTO
 * DTO for detailed order response with lines, crafts, and parts.
 */
export type OrderDetailResponseDto = {
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * External Skc No
     * 外部款号
     */
    external_skc_no?: string | null;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * External Order No
     * 外部订单号
     */
    external_order_no?: string | null;
    /**
     * External Order No2
     * 外部订单号2
     */
    external_order_no2?: string | null;
    /**
     * Cost
     * 成本
     */
    cost?: string | null;
    /**
     * Price
     * 价格
     */
    price?: string | null;
    /**
     * Expect Finished At
     * 预期完成时间
     */
    expect_finished_at?: string | null;
    /**
     * Owner User Id
     * 负责人用户ID
     */
    owner_user_id?: number | null;
    /**
     * Description
     * 订单描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Id
     * 订单ID
     */
    id: number;
    /**
     * Total Amount
     * 总数量
     */
    total_amount: number;
    /**
     * Status
     * 订单状态
     */
    status: string;
    /**
     * Current Craft
     * 当前工艺
     */
    current_craft?: string | null;
    /**
     * Current Craft Route
     * 当前工艺路线
     */
    current_craft_route?: number | null;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage?: number | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Finished At
     * 完成时间
     */
    finished_at?: string | null;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
    /**
     * Order Lines
     * 订单行列表
     */
    order_lines: Array<OrderLineResponseDto>;
    /**
     * Order Crafts
     * 订单工艺配置列表
     */
    order_crafts?: Array<OrderCraftResponseDto> | null;
    /**
     * Order Parts
     * 订单部位列表
     */
    order_parts?: Array<OrderPartResponseDto> | null;
};

/**
 * OrderLineCreateDTO
 * DTO for creating a new order line.
 */
export type OrderLineCreateDto = {
    /**
     * Size
     * 尺码
     */
    size: string;
    /**
     * Amount
     * 数量
     */
    amount: number;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * OrderLineProductionUpdateDTO
 * DTO for updating order line production.
 */
export type OrderLineProductionUpdateDto = {
    /**
     * Order Line Id
     * 订单行ID
     */
    order_line_id: number;
    /**
     * Produced Amount
     * 已生产数量
     */
    produced_amount?: number | null;
    /**
     * Completed Amount
     * 已完成数量
     */
    completed_amount?: number | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * OrderLineResponseDTO
 * DTO for order line response.
 */
export type OrderLineResponseDto = {
    /**
     * Size
     * 尺码
     */
    size: string;
    /**
     * Amount
     * 数量
     */
    amount: number;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Id
     * 订单行ID
     */
    id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Line No
     * 订单行号
     */
    order_line_no: string;
    /**
     * Produced Amount
     * 已生产数量
     */
    produced_amount?: number | null;
    /**
     * Completed Amount
     * 已完成数量
     */
    completed_amount?: number | null;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage?: number | null;
    /**
     * Production Percentage
     * 生产百分比
     */
    production_percentage?: number | null;
    /**
     * Remaining Amount
     * 剩余数量
     */
    remaining_amount?: number | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
};

/**
 * OrderLineStatisticsDTO
 * DTO for order line statistics.
 */
export type OrderLineStatisticsDto = {
    /**
     * Total Lines
     * 总订单行数
     */
    total_lines: number;
    /**
     * Completed Lines
     * 已完成订单行数
     */
    completed_lines: number;
    /**
     * In Progress Lines
     * 进行中订单行数
     */
    in_progress_lines: number;
    /**
     * Pending Lines
     * 待处理订单行数
     */
    pending_lines: number;
    /**
     * Total Amount
     * 总数量
     */
    total_amount: number;
    /**
     * Total Produced
     * 总生产数量
     */
    total_produced: number;
    /**
     * Total Completed
     * 总完成数量
     */
    total_completed: number;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage: number;
    /**
     * Production Percentage
     * 生产百分比
     */
    production_percentage: number;
};

/**
 * OrderListDTO
 * DTO for order list response.
 */
export type OrderListDto = {
    /**
     * Orders
     * 订单列表
     */
    orders: Array<OrderResponseDto>;
    /**
     * Total
     * 总数量
     */
    total: number;
};

/**
 * OrderOperationResultDTO
 * DTO for order operation results.
 */
export type OrderOperationResultDto = {
    /**
     * Success
     * 操作是否成功
     */
    success: boolean;
    /**
     * Message
     * 结果消息
     */
    message: string;
    /**
     * Order Id
     * 订单ID
     */
    order_id?: number | null;
    /**
     * Order No
     * 订单号
     */
    order_no?: string | null;
    /**
     * Details
     * 详细信息
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * OrderPartCreateDTO
 * DTO for creating a new order part.
 */
export type OrderPartCreateDto = {
    /**
     * Part Name
     * 部位名称
     */
    part_name: string;
    /**
     * 部位类型
     */
    part_type: PartTypeEnum;
    /**
     * Color
     * 颜色
     */
    color: string;
    /**
     * Total Quantity
     * 部位总件数
     */
    total_quantity: number;
    /**
     * Description
     * 部位描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Machine No
     * 机床编号
     */
    machine_no?: string | null;
    /**
     * Process Route
     * 工序路线
     */
    process_route?: string | null;
    /**
     * Supervisor User Id
     * 负责人用户ID
     */
    supervisor_user_id?: number | null;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Part Sequence
     * 部位序号(同一订单内)
     */
    part_sequence: number;
    /**
     * Order Bundles
     * 订单扎配置列表
     */
    order_bundles?: Array<OrderBundleCreateDto> | null;
};

/**
 * OrderPartListDTO
 * DTO for order part list response.
 */
export type OrderPartListDto = {
    /**
     * Order Parts
     * 订单部位列表
     */
    order_parts: Array<OrderPartResponseDto>;
    /**
     * Total
     * 总数量
     */
    total: number;
};

/**
 * OrderPartOperationResultDTO
 * DTO for order part operation results.
 */
export type OrderPartOperationResultDto = {
    /**
     * Success
     * 操作是否成功
     */
    success: boolean;
    /**
     * Message
     * 结果消息
     */
    message: string;
    /**
     * Order Part Id
     * 订单部位ID
     */
    order_part_id?: number | null;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no?: string | null;
    /**
     * Details
     * 详细信息
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * OrderPartResponseDTO
 * DTO for order part response.
 */
export type OrderPartResponseDto = {
    /**
     * Part Name
     * 部位名称
     */
    part_name: string;
    /**
     * 部位类型
     */
    part_type: PartTypeEnum;
    /**
     * Color
     * 颜色
     */
    color: string;
    /**
     * Total Quantity
     * 部位总件数
     */
    total_quantity: number;
    /**
     * Description
     * 部位描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Machine No
     * 机床编号
     */
    machine_no?: string | null;
    /**
     * Process Route
     * 工序路线
     */
    process_route?: string | null;
    /**
     * Supervisor User Id
     * 负责人用户ID
     */
    supervisor_user_id?: number | null;
    /**
     * Id
     * 订单部位ID
     */
    id: number;
    /**
     * Factory Id
     * 工厂ID
     */
    factory_id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Part Sequence
     * 部位序号
     */
    part_sequence: number;
    /**
     * 部位状态
     */
    status: PartStatusEnum;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity: number;
    /**
     * Progress Percentage
     * 完成百分比
     */
    progress_percentage: number;
    /**
     * Actual Start Date
     * 实际开始时间
     */
    actual_start_date?: string | null;
    /**
     * Actual End Date
     * 实际完成时间
     */
    actual_end_date?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
    /**
     * Supervisor Name
     * 负责人姓名
     */
    supervisor_name?: string | null;
};

/**
 * OrderPartStatisticsDTO
 * DTO for order part statistics.
 */
export type OrderPartStatisticsDto = {
    /**
     * Total Order Parts
     * 总订单部位数
     */
    total_order_parts: number;
    /**
     * Total Quantity
     * 总件数
     */
    total_quantity: number;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity: number;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage: number;
    /**
     * Status Breakdown
     * 状态分解
     */
    status_breakdown: {
        [key: string]: unknown;
    };
    /**
     * Planned Parts
     * 计划中部位数
     */
    planned_parts: number;
    /**
     * Cutting Parts
     * 裁剪中部位数
     */
    cutting_parts: number;
    /**
     * Sewing Parts
     * 缝制中部位数
     */
    sewing_parts: number;
    /**
     * Quality Check Parts
     * 质检中部位数
     */
    quality_check_parts: number;
    /**
     * Completed Parts
     * 已完成部位数
     */
    completed_parts: number;
    /**
     * On Hold Parts
     * 暂停部位数
     */
    on_hold_parts: number;
    /**
     * Cancelled Parts
     * 已取消部位数
     */
    cancelled_parts: number;
};

/**
 * OrderPartStatusUpdateDTO
 * DTO for updating order part status.
 */
export type OrderPartStatusUpdateDto = {
    /**
     * 新状态
     */
    status: PartStatusEnum;
    /**
     * Notes
     * 状态变更备注
     */
    notes?: string | null;
};

/**
 * OrderPartUpdateDTO
 * DTO for updating an order part.
 */
export type OrderPartUpdateDto = {
    /**
     * Part Name
     * 部位名称
     */
    part_name?: string | null;
    /**
     * Color
     * 颜色
     */
    color?: string | null;
    /**
     * Total Quantity
     * 部位总件数
     */
    total_quantity?: number | null;
    /**
     * 部位状态
     */
    status?: PartStatusEnum | null;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity?: number | null;
    /**
     * Description
     * 部位描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Actual Start Date
     * 实际开始时间
     */
    actual_start_date?: string | null;
    /**
     * Actual End Date
     * 实际完成时间
     */
    actual_end_date?: string | null;
    /**
     * Machine No
     * 机床编号
     */
    machine_no?: string | null;
    /**
     * Process Route
     * 工序路线
     */
    process_route?: string | null;
    /**
     * Supervisor User Id
     * 负责人用户ID
     */
    supervisor_user_id?: number | null;
};

/**
 * OrderPartWithBundlesDTO
 * DTO for order part with its order bundles.
 */
export type OrderPartWithBundlesDto = {
    /**
     * Part Name
     * 部位名称
     */
    part_name: string;
    /**
     * 部位类型
     */
    part_type: PartTypeEnum;
    /**
     * Color
     * 颜色
     */
    color: string;
    /**
     * Total Quantity
     * 部位总件数
     */
    total_quantity: number;
    /**
     * Description
     * 部位描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Planned Start Date
     * 计划开始时间
     */
    planned_start_date?: string | null;
    /**
     * Planned End Date
     * 计划完成时间
     */
    planned_end_date?: string | null;
    /**
     * Machine No
     * 机床编号
     */
    machine_no?: string | null;
    /**
     * Process Route
     * 工序路线
     */
    process_route?: string | null;
    /**
     * Supervisor User Id
     * 负责人用户ID
     */
    supervisor_user_id?: number | null;
    /**
     * Id
     * 订单部位ID
     */
    id: number;
    /**
     * Factory Id
     * 工厂ID
     */
    factory_id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Order Part No
     * 订单部位号
     */
    order_part_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Part Sequence
     * 部位序号
     */
    part_sequence: number;
    /**
     * 部位状态
     */
    status: PartStatusEnum;
    /**
     * Completed Quantity
     * 已完成件数
     */
    completed_quantity: number;
    /**
     * Progress Percentage
     * 完成百分比
     */
    progress_percentage: number;
    /**
     * Actual Start Date
     * 实际开始时间
     */
    actual_start_date?: string | null;
    /**
     * Actual End Date
     * 实际完成时间
     */
    actual_end_date?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
    /**
     * Supervisor Name
     * 负责人姓名
     */
    supervisor_name?: string | null;
    /**
     * Order Bundles
     * 订单扎列表
     */
    order_bundles: Array<OrderBundleResponseDto>;
};

/**
 * OrderProductionUpdateDTO
 * DTO for updating order line production.
 */
export type OrderProductionUpdateDto = {
    /**
     * Order Line Updates
     * 订单行更新列表
     */
    order_line_updates: Array<OrderLineProductionUpdateDto>;
};

/**
 * OrderResponseDTO
 * DTO for order response.
 */
export type OrderResponseDto = {
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * External Skc No
     * 外部款号
     */
    external_skc_no?: string | null;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * External Order No
     * 外部订单号
     */
    external_order_no?: string | null;
    /**
     * External Order No2
     * 外部订单号2
     */
    external_order_no2?: string | null;
    /**
     * Cost
     * 成本
     */
    cost?: string | null;
    /**
     * Price
     * 价格
     */
    price?: string | null;
    /**
     * Expect Finished At
     * 预期完成时间
     */
    expect_finished_at?: string | null;
    /**
     * Owner User Id
     * 负责人用户ID
     */
    owner_user_id?: number | null;
    /**
     * Description
     * 订单描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
    /**
     * Id
     * 订单ID
     */
    id: number;
    /**
     * Total Amount
     * 总数量
     */
    total_amount: number;
    /**
     * Status
     * 订单状态
     */
    status: string;
    /**
     * Current Craft
     * 当前工艺
     */
    current_craft?: string | null;
    /**
     * Current Craft Route
     * 当前工艺路线
     */
    current_craft_route?: number | null;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage?: number | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
    /**
     * Started At
     * 开始时间
     */
    started_at?: string | null;
    /**
     * Finished At
     * 完成时间
     */
    finished_at?: string | null;
    /**
     * Updated At
     * 更新时间
     */
    updated_at: string;
};

/**
 * OrderStatisticsDTO
 * DTO for order statistics.
 */
export type OrderStatisticsDto = {
    /**
     * Total Orders
     * 总订单数
     */
    total_orders: number;
    /**
     * Active Orders
     * 活跃订单数
     */
    active_orders: number;
    /**
     * Completed Orders
     * 已完成订单数
     */
    completed_orders: number;
    /**
     * Overdue Orders
     * 逾期订单数
     */
    overdue_orders: number;
    /**
     * Status Breakdown
     * 状态分解
     */
    status_breakdown: {
        [key: string]: unknown;
    };
    /**
     * Total Amount
     * 总数量
     */
    total_amount: number;
    /**
     * Total Completed Amount
     * 总完成数量
     */
    total_completed_amount: number;
    /**
     * Completion Rate
     * 完成率
     */
    completion_rate: number;
};

/**
 * OrderStatusUpdateDTO
 * DTO for updating order status.
 */
export type OrderStatusUpdateDto = {
    /**
     * Status
     * 新状态
     */
    status: string;
    /**
     * Notes
     * 状态变更备注
     */
    notes?: string | null;
};

/**
 * OrderSummaryDTO
 * DTO for order summary information.
 */
export type OrderSummaryDto = {
    /**
     * Id
     * 订单ID
     */
    id: number;
    /**
     * Order No
     * 订单号
     */
    order_no: string;
    /**
     * Skc No
     * 款号
     */
    skc_no: string;
    /**
     * Status
     * 订单状态
     */
    status: string;
    /**
     * Total Amount
     * 总数量
     */
    total_amount: number;
    /**
     * Completed Amount
     * 已完成数量
     */
    completed_amount: number;
    /**
     * Completion Percentage
     * 完成百分比
     */
    completion_percentage: number;
    /**
     * Owner Name
     * 负责人姓名
     */
    owner_name?: string | null;
    /**
     * Current Craft
     * 当前工艺
     */
    current_craft?: string | null;
    /**
     * Expect Finished At
     * 预期完成时间
     */
    expect_finished_at?: string | null;
    /**
     * Created At
     * 创建时间
     */
    created_at: string;
};

/**
 * OrderUpdateDTO
 * DTO for updating an order.
 */
export type OrderUpdateDto = {
    /**
     * Skc No
     * 款号
     */
    skc_no?: string | null;
    /**
     * External Skc No
     * 外部款号
     */
    external_skc_no?: string | null;
    /**
     * External Order No
     * 外部订单号
     */
    external_order_no?: string | null;
    /**
     * External Order No2
     * 外部订单号2
     */
    external_order_no2?: string | null;
    /**
     * Cost
     * 成本
     */
    cost?: number | string | null;
    /**
     * Price
     * 价格
     */
    price?: number | string | null;
    /**
     * Expect Finished At
     * 预期完成时间
     */
    expect_finished_at?: string | null;
    /**
     * Owner User Id
     * 负责人用户ID
     */
    owner_user_id?: number | null;
    /**
     * Description
     * 订单描述
     */
    description?: string | null;
    /**
     * Notes
     * 备注
     */
    notes?: string | null;
};

/**
 * PartStatusEnum
 * Part status enumeration for DTOs.
 */
export type PartStatusEnum = 'planned' | 'cutting' | 'sewing' | 'quality_check' | 'completed' | 'on_hold' | 'cancelled';

/**
 * PartTypeEnum
 * Part type enumeration for DTOs.
 */
export type PartTypeEnum = 'front_body' | 'back_body' | 'sleeve' | 'collar' | 'pocket' | 'cuff' | 'waistband' | 'leg' | 'zipper' | 'button_placket' | 'lining' | 'accessories' | 'other';

/**
 * PaymentMethod
 * 支付方式枚举
 */
export type PaymentMethod = 'cash' | 'bank_transfer' | 'mobile_pay' | 'check' | 'other';

/**
 * PermissionListDTO
 * DTO for permission list response.
 */
export type PermissionListDto = {
    /**
     * Permissions
     * List of permissions
     */
    permissions: Array<PermissionResponseDto>;
    /**
     * Total
     * Total number of permissions
     */
    total: number;
};

/**
 * PermissionResponseDTO
 * DTO for permission response.
 */
export type PermissionResponseDto = {
    /**
     * Code
     * Unique permission code
     */
    code: string;
    /**
     * Name
     * Permission name
     */
    name: string;
    /**
     * Parent Id
     * Parent permission ID for hierarchical structure
     */
    parent_id?: number | null;
    /**
     * Id
     * Permission ID
     */
    id: number;
};

/**
 * PermissionTreeDTO
 * DTO for hierarchical permission tree.
 */
export type PermissionTreeDto = {
    /**
     * Id
     * Permission ID
     */
    id: number;
    /**
     * Code
     * Permission code
     */
    code: string;
    /**
     * Name
     * Permission name
     */
    name: string;
    /**
     * Parent Id
     * Parent permission ID
     */
    parent_id?: number | null;
    /**
     * Children
     * Child permissions
     */
    children?: Array<PermissionTreeDto>;
};

/**
 * PhonePasswordLoginDTO
 * DTO for phone + password + image validation code login.
 */
export type PhonePasswordLoginDto = {
    /**
     * Phone
     * Chinese mobile number
     */
    phone: string;
    /**
     * Password
     */
    password: string;
    /**
     * Image Code
     */
    image_code: string;
    /**
     * Session Id
     * Session ID for image validation code
     */
    session_id: string;
};

/**
 * PhoneSmsLoginDTO
 * DTO for phone + SMS + image validation code login.
 */
export type PhoneSmsLoginDto = {
    /**
     * Phone
     * Chinese mobile number
     */
    phone: string;
    /**
     * Sms Code
     */
    sms_code: string;
    /**
     * Image Code
     */
    image_code: string;
    /**
     * Session Id
     * Session ID for image validation code
     */
    session_id: string;
};

/**
 * RegistrationSummaryDTO
 * 登记汇总信息
 */
export type RegistrationSummaryDto = {
    /**
     * Total Craft Routes
     * 总工艺路线数
     */
    total_craft_routes: number;
    /**
     * Available Craft Routes
     * 可登记工艺路线数
     */
    available_craft_routes: number;
    /**
     * Total Parts
     * 总部位数
     */
    total_parts: number;
    /**
     * Available Parts
     * 可登记部位数
     */
    available_parts: number;
    /**
     * Total Bundles
     * 总扎数
     */
    total_bundles: number;
    /**
     * Available Bundles
     * 可登记扎数
     */
    available_bundles: number;
    /**
     * Completion Rate
     * 完成率 (0-1)
     */
    completion_rate: number;
};

/**
 * RemoveSkillDTO
 * DTO for removing skill from user.
 */
export type RemoveSkillDto = {
    /**
     * User Factory Skill Id
     * User factory skill ID
     */
    user_factory_skill_id: number;
    /**
     * Reason
     * Reason for removal
     */
    reason?: string | null;
};

/**
 * RemoveUserFromFactoryDTO
 * DTO for removing user from factory.
 */
export type RemoveUserFromFactoryDto = {
    /**
     * User Id
     * User ID to remove
     */
    user_id: number;
    /**
     * Reason
     * Reason for removal
     */
    reason?: string | null;
};

/**
 * RoleCreateDTO
 * DTO for creating a new role.
 */
export type RoleCreateDto = {
    /**
     * Name
     * Unique role name
     */
    name: string;
    /**
     * Description
     * Role description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the role is active
     */
    is_active?: boolean;
    /**
     * Permission Ids
     * List of permission IDs to assign
     */
    permission_ids?: Array<number>;
};

/**
 * RoleListDTO
 * DTO for role list response.
 */
export type RoleListDto = {
    /**
     * Roles
     * List of roles
     */
    roles: Array<RoleResponseDto>;
    /**
     * Total
     * Total number of roles
     */
    total: number;
};

/**
 * RolePermissionAssignDTO
 * DTO for assigning permissions to role.
 */
export type RolePermissionAssignDto = {
    /**
     * Permission Ids
     * List of permission IDs to assign
     */
    permission_ids: Array<number>;
};

/**
 * RolePermissionRemoveDTO
 * DTO for removing permissions from role.
 */
export type RolePermissionRemoveDto = {
    /**
     * Permission Ids
     * List of permission IDs to remove
     */
    permission_ids: Array<number>;
};

/**
 * RoleResponseDTO
 * DTO for role response.
 */
export type RoleResponseDto = {
    /**
     * Name
     * Unique role name
     */
    name: string;
    /**
     * Description
     * Role description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the role is active
     */
    is_active?: boolean;
    /**
     * Id
     * Role ID
     */
    id: number;
    /**
     * Permissions
     * Assigned permissions
     */
    permissions?: Array<PermissionResponseDto>;
};

/**
 * RoleSummaryDTO
 * DTO for role summary without permissions.
 */
export type RoleSummaryDto = {
    /**
     * Id
     * Role ID
     */
    id: number;
    /**
     * Name
     * Role name
     */
    name: string;
    /**
     * Description
     * Role description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the role is active
     */
    is_active: boolean;
    /**
     * Permission Count
     * Number of permissions assigned
     */
    permission_count: number;
};

/**
 * RoleUpdateDTO
 * DTO for updating a role.
 */
export type RoleUpdateDto = {
    /**
     * Name
     * Role name
     */
    name?: string | null;
    /**
     * Description
     * Role description
     */
    description?: string | null;
    /**
     * Is Active
     * Whether the role is active
     */
    is_active?: boolean | null;
};

/**
 * SendSmsCodeDTO
 * DTO for requesting SMS validation code.
 */
export type SendSmsCodeDto = {
    /**
     * Phone
     * Chinese mobile number
     */
    phone: string;
    /**
     * Image Code
     */
    image_code: string;
    /**
     * Session Id
     * Session ID for image validation code
     */
    session_id: string;
};

/**
 * SendSmsCodeResponseDTO
 * Response DTO for SMS code sending.
 */
export type SendSmsCodeResponseDto = {
    /**
     * Success
     */
    success: boolean;
    /**
     * Message
     */
    message: string;
    /**
     * Expires In Seconds
     */
    expires_in_seconds: number;
};

/**
 * SessionFactoryContextDTO
 * DTO for factory context in session.
 */
export type SessionFactoryContextDto = {
    /**
     * Factory Id
     */
    factory_id?: number | null;
    /**
     * Factory Name
     */
    factory_name?: string | null;
    /**
     * Factory Code
     */
    factory_code?: string | null;
    /**
     * Department Id
     */
    department_id?: number | null;
    /**
     * Department Name
     */
    department_name?: string | null;
    /**
     * Role
     */
    role?: string | null;
    /**
     * Employee Id
     */
    employee_id?: string | null;
    /**
     * Position
     */
    position?: string | null;
    /**
     * Is Manager
     */
    is_manager?: boolean;
};

/**
 * SessionStatusDTO
 * DTO for session status response.
 */
export type SessionStatusDto = {
    /**
     * Is Valid
     */
    is_valid: boolean;
    user_session?: UserSessionDto | null;
    /**
     * Message
     */
    message?: string | null;
};

/**
 * SettlementStatusDTO
 * 结算状态枚举
 */
export type SettlementStatusDto = 'pending' | 'included' | 'settled' | 'disputed' | 'excluded';

/**
 * SkillCreateDTO
 * DTO for creating a new skill.
 */
export type SkillCreateDto = {
    /**
     * Code
     * Unique skill code
     */
    code: string;
    /**
     * Name
     * Skill name
     */
    name: string;
    /**
     * Description
     * Skill description
     */
    description?: string | null;
    /**
     * Category
     * Skill category
     */
    category?: string | null;
    /**
     * Is Active
     * Whether the skill is active
     */
    is_active?: boolean;
};

/**
 * SkillListDTO
 * DTO for skill list response.
 */
export type SkillListDto = {
    /**
     * Skills
     * List of skills
     */
    skills: Array<SkillResponseDto>;
    /**
     * Total
     * Total number of skills
     */
    total: number;
};

/**
 * SkillOperationResultDTO
 * DTO for skill operation results.
 */
export type SkillOperationResultDto = {
    /**
     * Success
     * Whether operation succeeded
     */
    success: boolean;
    /**
     * Message
     * Result message
     */
    message: string;
    /**
     * Skill Id
     * Skill ID affected
     */
    skill_id?: number | null;
    /**
     * User Factory Skill Id
     * User factory skill ID affected
     */
    user_factory_skill_id?: number | null;
    /**
     * Details
     * Additional operation details
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * SkillResponseDTO
 * DTO for skill response.
 */
export type SkillResponseDto = {
    /**
     * Code
     * Unique skill code
     */
    code: string;
    /**
     * Name
     * Skill name
     */
    name: string;
    /**
     * Description
     * Skill description
     */
    description?: string | null;
    /**
     * Category
     * Skill category
     */
    category?: string | null;
    /**
     * Is Active
     * Whether the skill is active
     */
    is_active?: boolean;
    /**
     * Id
     * Skill ID
     */
    id: number;
};

/**
 * SkillUpdateDTO
 * DTO for updating a skill.
 */
export type SkillUpdateDto = {
    /**
     * Name
     * Skill name
     */
    name?: string | null;
    /**
     * Description
     * Skill description
     */
    description?: string | null;
    /**
     * Category
     * Skill category
     */
    category?: string | null;
    /**
     * Is Active
     * Whether the skill is active
     */
    is_active?: boolean | null;
};

/**
 * SuspendUserDTO
 * DTO for suspending user.
 */
export type SuspendUserDto = {
    /**
     * User Id
     * User ID to suspend
     */
    user_id: number;
    /**
     * Reason
     * Reason for suspension
     */
    reason?: string | null;
    /**
     * Suspension Note
     * Additional notes
     */
    suspension_note?: string | null;
};

/**
 * SwitchFactoryDTO
 * DTO for switching factory context.
 */
export type SwitchFactoryDto = {
    /**
     * Factory Id
     */
    factory_id: number;
};

/**
 * UpdateBillRequestDTO
 * DTO for updating a bill.
 */
export type UpdateBillRequestDto = {
    /**
     * Total Completed Quantity
     * Total completed quantity
     */
    total_completed_quantity?: number | null;
    /**
     * Total Work Instances
     * Total work instances
     */
    total_work_instances?: number | null;
    /**
     * Total Work Duration Minutes
     * Total work duration in minutes
     */
    total_work_duration_minutes?: number | null;
    /**
     * Base Amount
     * Base amount
     */
    base_amount?: number | string | null;
    /**
     * Bonus Amount
     * Bonus amount
     */
    bonus_amount?: number | string | null;
    /**
     * Deduction Amount
     * Deduction amount
     */
    deduction_amount?: number | string | null;
    /**
     * Total Amount
     * Total amount
     */
    total_amount?: number | string | null;
    /**
     * Quality Score Average
     * Average quality score
     */
    quality_score_average?: number | string | null;
    /**
     * Defect Count
     * Defect count
     */
    defect_count?: number | null;
    /**
     * Rework Count
     * Rework count
     */
    rework_count?: number | null;
    /**
     * Breakdown By Granularity
     * Breakdown by granularity
     */
    breakdown_by_granularity?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Craft Route
     * Breakdown by craft route
     */
    breakdown_by_craft_route?: {
        [key: string]: unknown;
    } | null;
    /**
     * Breakdown By Order
     * Breakdown by order
     */
    breakdown_by_order?: {
        [key: string]: unknown;
    } | null;
    /**
     * Notes
     * Notes
     */
    notes?: string | null;
};

/**
 * UpdateSkillProficiencyDTO
 * DTO for updating skill proficiency.
 */
export type UpdateSkillProficiencyDto = {
    /**
     * User Factory Skill Id
     * User factory skill ID
     */
    user_factory_skill_id: number;
    /**
     * Proficiency Level
     * New proficiency level
     */
    proficiency_level: string;
    /**
     * Notes
     * Updated notes
     */
    notes?: string | null;
};

/**
 * UserCreateDTO
 */
export type UserCreateDto = {
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Password
     */
    password: string;
    /**
     * Full Name
     */
    full_name?: string | null;
};

/**
 * UserFactoryResponseDTO
 * Response DTO for UserFactory relationships.
 */
export type UserFactoryResponseDto = {
    /**
     * Id
     */
    id: number;
    /**
     * User Id
     */
    user_id: number;
    /**
     * Factory Id
     */
    factory_id: number;
    /**
     * Department Id
     */
    department_id: number | null;
    status: UserFactoryStatusEnum;
    role: UserFactoryRoleEnum;
    /**
     * Employee Id
     */
    employee_id: string | null;
    /**
     * Position
     */
    position: string | null;
    /**
     * Start Date
     */
    start_date: string | null;
    /**
     * End Date
     */
    end_date: string | null;
    /**
     * Requested At
     */
    requested_at: string;
    /**
     * Approved By
     */
    approved_by: number | null;
    /**
     * Approved At
     */
    approved_at: string | null;
    /**
     * Rejected Reason
     */
    rejected_reason: string | null;
    /**
     * Factory Name
     */
    factory_name?: string | null;
    /**
     * Department Name
     */
    department_name?: string | null;
    /**
     * User Name
     */
    user_name?: string | null;
    /**
     * Approver Name
     */
    approver_name?: string | null;
};

/**
 * UserFactoryRole
 * User's role in the factory.
 */
export type UserFactoryRole = 'worker' | 'supervisor' | 'manager' | 'admin';

/**
 * UserFactoryRoleEnum
 * Role enum for API responses.
 */
export type UserFactoryRoleEnum = 'worker' | 'supervisor' | 'manager' | 'admin';

/**
 * UserFactorySkillDTO
 * DTO for user factory skill relationship.
 */
export type UserFactorySkillDto = {
    /**
     * Id
     * User factory skill ID
     */
    id: number;
    /**
     * Skill information
     */
    skill: SkillResponseDto;
    /**
     * Proficiency Level
     * Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
     */
    proficiency_level: string;
    /**
     * Certified
     * Whether user is certified
     */
    certified: boolean;
    /**
     * Certification Date
     * Certification date (ISO format)
     */
    certification_date?: string | null;
    /**
     * Certification Expires
     * Certification expiry date (ISO format)
     */
    certification_expires?: string | null;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
    /**
     * Assigned By
     * ID of user who assigned/updated this skill
     */
    assigned_by?: number | null;
    /**
     * Assigned At
     * When skill was assigned (ISO format)
     */
    assigned_at: string;
    /**
     * Updated At
     * Last update time (ISO format)
     */
    updated_at: string;
};

/**
 * UserFactoryStatusEnum
 * Status enum for API responses.
 */
export type UserFactoryStatusEnum = 'pending' | 'approved' | 'rejected' | 'suspended' | 'resigned';

/**
 * UserInfoDTO
 * DTO for user information in bill responses.
 */
export type UserInfoDto = {
    /**
     * Id
     */
    id: number;
    /**
     * Username
     */
    username: string;
    /**
     * Full Name
     */
    full_name?: string | null;
};

/**
 * UserOperationResultDTO
 * DTO for user operation results.
 */
export type UserOperationResultDto = {
    /**
     * Success
     * Whether operation succeeded
     */
    success: boolean;
    /**
     * Message
     * Result message
     */
    message: string;
    /**
     * User Id
     * User ID affected
     */
    user_id: number;
    /**
     * Details
     * Additional operation details
     */
    details?: {
        [key: string]: unknown;
    } | null;
};

/**
 * UserResponseDTO
 */
export type UserResponseDto = {
    /**
     * Id
     */
    id: number;
    /**
     * Username
     */
    username: string;
    /**
     * Email
     */
    email: string;
    /**
     * Full Name
     */
    full_name: string | null;
    /**
     * Is Active
     */
    is_active: boolean;
    /**
     * Is Superuser
     */
    is_superuser: boolean;
    /**
     * Avatar Url
     */
    avatar_url: string | null;
    /**
     * Created At
     */
    created_at: string;
    /**
     * Updated At
     */
    updated_at: string;
};

/**
 * UserSessionDTO
 * DTO for user session information.
 */
export type UserSessionDto = {
    /**
     * User Id
     */
    user_id: number;
    /**
     * Username
     */
    username: string;
    /**
     * Session Id
     */
    session_id: string;
    factory_context: SessionFactoryContextDto;
    /**
     * Session Created At
     */
    session_created_at: string;
};

/**
 * UserSkillCreateDTO
 * DTO for creating user skill.
 */
export type UserSkillCreateDto = {
    /**
     * Skill Id
     * Skill ID
     */
    skill_id: number;
    /**
     * Proficiency Level
     * Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
     */
    proficiency_level?: string;
    /**
     * Certified
     * Whether user is initially certified
     */
    certified?: boolean;
    /**
     * Certification Date
     * Certification date (ISO format)
     */
    certification_date?: string | null;
    /**
     * Certification Expires
     * Certification expiry date (ISO format)
     */
    certification_expires?: string | null;
    /**
     * Notes
     * Additional notes
     */
    notes?: string | null;
};

/**
 * UserSkillsDTO
 * DTO for user's skills in a factory.
 */
export type UserSkillsDto = {
    /**
     * User Factory Id
     * User factory relationship ID
     */
    user_factory_id: number;
    /**
     * Skills
     * List of user's skills
     */
    skills: Array<UserFactorySkillDto>;
    /**
     * Total Skills
     * Total number of skills
     */
    total_skills: number;
    /**
     * Certified Skills
     * Number of certified skills
     */
    certified_skills: number;
};

/**
 * UserSummaryDTO
 * DTO for user summary information.
 */
export type UserSummaryDto = {
    /**
     * Id
     * User ID
     */
    id: number;
    /**
     * Username
     * Username
     */
    username: string;
    /**
     * Full Name
     * User's full name
     */
    full_name?: string | null;
    /**
     * Email
     * Email address
     */
    email: string;
    /**
     * Phone
     * Phone number
     */
    phone?: string | null;
    /**
     * Is Active
     * Whether user is active
     */
    is_active: boolean;
    /**
     * User's system role
     */
    role?: RoleSummaryDto | null;
};

/**
 * UserUpdateDTO
 */
export type UserUpdateDto = {
    /**
     * Full Name
     */
    full_name?: string | null;
    /**
     * Avatar Url
     */
    avatar_url?: string | null;
    /**
     * Is Active
     */
    is_active?: boolean | null;
};

/**
 * ValidationError
 */
export type ValidationError = {
    /**
     * Location
     */
    loc: Array<string | number>;
    /**
     * Message
     */
    msg: string;
    /**
     * Error Type
     */
    type: string;
};

/**
 * TokenResponseDTO
 * Response DTO for authentication token.
 */
export type SrcApplicationDtoAuthDtoTokenResponseDto = {
    /**
     * Access Token
     */
    access_token: string;
    /**
     * Token Type
     */
    token_type?: string;
    /**
     * Session Id
     */
    session_id: string;
    /**
     * User Info
     */
    user_info: {
        [key: string]: unknown;
    };
    /**
     * Factory Context
     */
    factory_context: {
        [key: string]: unknown;
    };
};

/**
 * TokenResponseDTO
 */
export type SrcApplicationDtoUserDtoTokenResponseDto = {
    /**
     * Access Token
     */
    access_token: string;
    /**
     * Token Type
     */
    token_type?: string;
    /**
     * Session Id
     */
    session_id?: string | null;
    factory_context?: FactoryContextDto | null;
};

export type LoginApiV1AuthTokenPostData = {
    body: BodyLoginApiV1AuthTokenPost;
    path?: never;
    query?: never;
    url: '/api/v1/auth/token';
};

export type LoginApiV1AuthTokenPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type LoginApiV1AuthTokenPostError = LoginApiV1AuthTokenPostErrors[keyof LoginApiV1AuthTokenPostErrors];

export type LoginApiV1AuthTokenPostResponses = {
    /**
     * Successful Response
     */
    200: SrcApplicationDtoUserDtoTokenResponseDto;
};

export type LoginApiV1AuthTokenPostResponse = LoginApiV1AuthTokenPostResponses[keyof LoginApiV1AuthTokenPostResponses];

export type ReadUsersMeApiV1AuthMeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/auth/me';
};

export type ReadUsersMeApiV1AuthMeGetResponses = {
    /**
     * Successful Response
     */
    200: UserResponseDto;
};

export type ReadUsersMeApiV1AuthMeGetResponse = ReadUsersMeApiV1AuthMeGetResponses[keyof ReadUsersMeApiV1AuthMeGetResponses];

export type GenerateImageCodeApiV1AuthGenerateImageCodePostData = {
    body: GenerateImageCodeDto;
    path?: never;
    query?: never;
    url: '/api/v1/auth/generate-image-code';
};

export type GenerateImageCodeApiV1AuthGenerateImageCodePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GenerateImageCodeApiV1AuthGenerateImageCodePostError = GenerateImageCodeApiV1AuthGenerateImageCodePostErrors[keyof GenerateImageCodeApiV1AuthGenerateImageCodePostErrors];

export type GenerateImageCodeApiV1AuthGenerateImageCodePostResponses = {
    /**
     * Successful Response
     */
    200: ImageCodeResponseDto;
};

export type GenerateImageCodeApiV1AuthGenerateImageCodePostResponse = GenerateImageCodeApiV1AuthGenerateImageCodePostResponses[keyof GenerateImageCodeApiV1AuthGenerateImageCodePostResponses];

export type SendSmsCodeApiV1AuthSendSmsCodePostData = {
    body: SendSmsCodeDto;
    path?: never;
    query?: never;
    url: '/api/v1/auth/send-sms-code';
};

export type SendSmsCodeApiV1AuthSendSmsCodePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SendSmsCodeApiV1AuthSendSmsCodePostError = SendSmsCodeApiV1AuthSendSmsCodePostErrors[keyof SendSmsCodeApiV1AuthSendSmsCodePostErrors];

export type SendSmsCodeApiV1AuthSendSmsCodePostResponses = {
    /**
     * Successful Response
     */
    200: SendSmsCodeResponseDto;
};

export type SendSmsCodeApiV1AuthSendSmsCodePostResponse = SendSmsCodeApiV1AuthSendSmsCodePostResponses[keyof SendSmsCodeApiV1AuthSendSmsCodePostResponses];

export type LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostData = {
    body: PhonePasswordLoginDto;
    path?: never;
    query?: never;
    url: '/api/v1/auth/login-phone-password';
};

export type LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostError = LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostErrors[keyof LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostErrors];

export type LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponses = {
    /**
     * Successful Response
     */
    200: SrcApplicationDtoAuthDtoTokenResponseDto;
};

export type LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponse = LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponses[keyof LoginWithPhonePasswordApiV1AuthLoginPhonePasswordPostResponses];

export type LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostData = {
    body: PhoneSmsLoginDto;
    path?: never;
    query?: never;
    url: '/api/v1/auth/login-phone-sms';
};

export type LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostError = LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostErrors[keyof LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostErrors];

export type LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponses = {
    /**
     * Successful Response
     */
    200: SrcApplicationDtoAuthDtoTokenResponseDto;
};

export type LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponse = LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponses[keyof LoginWithPhoneSmsApiV1AuthLoginPhoneSmsPostResponses];

export type CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/auth/cleanup-expired-codes';
};

export type CleanupExpiredCodesApiV1AuthCleanupExpiredCodesPostResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetUsersApiV1UsersGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Skip
         */
        skip?: number;
        /**
         * Limit
         */
        limit?: number;
    };
    url: '/api/v1/users/';
};

export type GetUsersApiV1UsersGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUsersApiV1UsersGetError = GetUsersApiV1UsersGetErrors[keyof GetUsersApiV1UsersGetErrors];

export type GetUsersApiV1UsersGetResponses = {
    /**
     * Response Get Users Api V1 Users  Get
     * Successful Response
     */
    200: Array<UserResponseDto>;
};

export type GetUsersApiV1UsersGetResponse = GetUsersApiV1UsersGetResponses[keyof GetUsersApiV1UsersGetResponses];

export type CreateUserApiV1UsersPostData = {
    body: UserCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/users/';
};

export type CreateUserApiV1UsersPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateUserApiV1UsersPostError = CreateUserApiV1UsersPostErrors[keyof CreateUserApiV1UsersPostErrors];

export type CreateUserApiV1UsersPostResponses = {
    /**
     * Successful Response
     */
    201: UserResponseDto;
};

export type CreateUserApiV1UsersPostResponse = CreateUserApiV1UsersPostResponses[keyof CreateUserApiV1UsersPostResponses];

export type DeleteUserApiV1UsersUserIdDeleteData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query?: never;
    url: '/api/v1/users/{user_id}';
};

export type DeleteUserApiV1UsersUserIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteUserApiV1UsersUserIdDeleteError = DeleteUserApiV1UsersUserIdDeleteErrors[keyof DeleteUserApiV1UsersUserIdDeleteErrors];

export type DeleteUserApiV1UsersUserIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteUserApiV1UsersUserIdDeleteResponse = DeleteUserApiV1UsersUserIdDeleteResponses[keyof DeleteUserApiV1UsersUserIdDeleteResponses];

export type GetUserApiV1UsersUserIdGetData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query?: never;
    url: '/api/v1/users/{user_id}';
};

export type GetUserApiV1UsersUserIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUserApiV1UsersUserIdGetError = GetUserApiV1UsersUserIdGetErrors[keyof GetUserApiV1UsersUserIdGetErrors];

export type GetUserApiV1UsersUserIdGetResponses = {
    /**
     * Successful Response
     */
    200: UserResponseDto;
};

export type GetUserApiV1UsersUserIdGetResponse = GetUserApiV1UsersUserIdGetResponses[keyof GetUserApiV1UsersUserIdGetResponses];

export type UpdateUserApiV1UsersUserIdPutData = {
    body: UserUpdateDto;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query?: never;
    url: '/api/v1/users/{user_id}';
};

export type UpdateUserApiV1UsersUserIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateUserApiV1UsersUserIdPutError = UpdateUserApiV1UsersUserIdPutErrors[keyof UpdateUserApiV1UsersUserIdPutErrors];

export type UpdateUserApiV1UsersUserIdPutResponses = {
    /**
     * Successful Response
     */
    200: UserResponseDto;
};

export type UpdateUserApiV1UsersUserIdPutResponse = UpdateUserApiV1UsersUserIdPutResponses[keyof UpdateUserApiV1UsersUserIdPutResponses];

export type RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostData = {
    body: FactoryJoinRequestDto;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/join-request';
};

export type RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostError = RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostErrors[keyof RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostErrors];

export type RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponses = {
    /**
     * Successful Response
     */
    200: UserFactoryResponseDto;
};

export type RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponse = RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponses[keyof RequestToJoinFactoryApiV1FactoryManagementJoinRequestPostResponses];

export type ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostData = {
    body: FactoryJoinApprovalDto;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/approve-request';
};

export type ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostError = ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostErrors[keyof ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostErrors];

export type ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponses = {
    /**
     * Successful Response
     */
    200: UserFactoryResponseDto;
};

export type ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponse = ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponses[keyof ApproveOrRejectRequestApiV1FactoryManagementApproveRequestPostResponses];

export type GetMyFactoriesApiV1FactoryManagementMyFactoriesGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/my-factories';
};

export type GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponses = {
    /**
     * Successful Response
     */
    200: MyFactoriesDto;
};

export type GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponse = GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponses[keyof GetMyFactoriesApiV1FactoryManagementMyFactoriesGetResponses];

export type GetPendingRequestsApiV1FactoryManagementPendingRequestsGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/pending-requests';
};

export type GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponses = {
    /**
     * Response Get Pending Requests Api V1 Factory Management Pending Requests Get
     * Successful Response
     */
    200: Array<UserFactoryResponseDto>;
};

export type GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponse = GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponses[keyof GetPendingRequestsApiV1FactoryManagementPendingRequestsGetResponses];

export type GetFactoryMembersApiV1FactoryManagementMembersGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/members';
};

export type GetFactoryMembersApiV1FactoryManagementMembersGetResponses = {
    /**
     * Response Get Factory Members Api V1 Factory Management Members Get
     * Successful Response
     */
    200: Array<UserFactoryResponseDto>;
};

export type GetFactoryMembersApiV1FactoryManagementMembersGetResponse = GetFactoryMembersApiV1FactoryManagementMembersGetResponses[keyof GetFactoryMembersApiV1FactoryManagementMembersGetResponses];

export type UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query: {
        new_role: UserFactoryRole;
    };
    url: '/api/v1/factory-management/user/{user_id}/role';
};

export type UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutError = UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutErrors[keyof UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutErrors];

export type UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponses = {
    /**
     * Successful Response
     */
    200: UserFactoryResponseDto;
};

export type UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponse = UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponses[keyof UpdateUserRoleApiV1FactoryManagementUserUserIdRolePutResponses];

export type SuspendUserApiV1FactoryManagementUserUserIdSuspendPostData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query?: {
        /**
         * Reason
         */
        reason?: string;
    };
    url: '/api/v1/factory-management/user/{user_id}/suspend';
};

export type SuspendUserApiV1FactoryManagementUserUserIdSuspendPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SuspendUserApiV1FactoryManagementUserUserIdSuspendPostError = SuspendUserApiV1FactoryManagementUserUserIdSuspendPostErrors[keyof SuspendUserApiV1FactoryManagementUserUserIdSuspendPostErrors];

export type SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponses = {
    /**
     * Successful Response
     */
    200: UserFactoryResponseDto;
};

export type SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponse = SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponses[keyof SuspendUserApiV1FactoryManagementUserUserIdSuspendPostResponses];

export type ResignFromFactoryApiV1FactoryManagementResignPostData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/factory-management/resign';
};

export type ResignFromFactoryApiV1FactoryManagementResignPostResponses = {
    /**
     * Successful Response
     */
    200: UserFactoryResponseDto;
};

export type ResignFromFactoryApiV1FactoryManagementResignPostResponse = ResignFromFactoryApiV1FactoryManagementResignPostResponses[keyof ResignFromFactoryApiV1FactoryManagementResignPostResponses];

export type GetSessionStatusApiV1SessionStatusGetData = {
    body?: never;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/status';
};

export type GetSessionStatusApiV1SessionStatusGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetSessionStatusApiV1SessionStatusGetError = GetSessionStatusApiV1SessionStatusGetErrors[keyof GetSessionStatusApiV1SessionStatusGetErrors];

export type GetSessionStatusApiV1SessionStatusGetResponses = {
    /**
     * Successful Response
     */
    200: SessionStatusDto;
};

export type GetSessionStatusApiV1SessionStatusGetResponse = GetSessionStatusApiV1SessionStatusGetResponses[keyof GetSessionStatusApiV1SessionStatusGetResponses];

export type GetAvailableFactoriesApiV1SessionAvailableFactoriesGetData = {
    body?: never;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/available-factories';
};

export type GetAvailableFactoriesApiV1SessionAvailableFactoriesGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAvailableFactoriesApiV1SessionAvailableFactoriesGetError = GetAvailableFactoriesApiV1SessionAvailableFactoriesGetErrors[keyof GetAvailableFactoriesApiV1SessionAvailableFactoriesGetErrors];

export type GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponses = {
    /**
     * Successful Response
     */
    200: AvailableFactoriesDto;
};

export type GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponse = GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponses[keyof GetAvailableFactoriesApiV1SessionAvailableFactoriesGetResponses];

export type SwitchFactoryContextApiV1SessionSwitchFactoryPostData = {
    body: SwitchFactoryDto;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/switch-factory';
};

export type SwitchFactoryContextApiV1SessionSwitchFactoryPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SwitchFactoryContextApiV1SessionSwitchFactoryPostError = SwitchFactoryContextApiV1SessionSwitchFactoryPostErrors[keyof SwitchFactoryContextApiV1SessionSwitchFactoryPostErrors];

export type SwitchFactoryContextApiV1SessionSwitchFactoryPostResponses = {
    /**
     * Successful Response
     */
    200: UserSessionDto;
};

export type SwitchFactoryContextApiV1SessionSwitchFactoryPostResponse = SwitchFactoryContextApiV1SessionSwitchFactoryPostResponses[keyof SwitchFactoryContextApiV1SessionSwitchFactoryPostResponses];

export type RefreshFactoryContextApiV1SessionRefreshContextPostData = {
    body?: never;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/refresh-context';
};

export type RefreshFactoryContextApiV1SessionRefreshContextPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RefreshFactoryContextApiV1SessionRefreshContextPostError = RefreshFactoryContextApiV1SessionRefreshContextPostErrors[keyof RefreshFactoryContextApiV1SessionRefreshContextPostErrors];

export type RefreshFactoryContextApiV1SessionRefreshContextPostResponses = {
    /**
     * Successful Response
     */
    200: UserSessionDto;
};

export type RefreshFactoryContextApiV1SessionRefreshContextPostResponse = RefreshFactoryContextApiV1SessionRefreshContextPostResponses[keyof RefreshFactoryContextApiV1SessionRefreshContextPostResponses];

export type ExtendSessionApiV1SessionExtendPostData = {
    body?: never;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/extend';
};

export type ExtendSessionApiV1SessionExtendPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ExtendSessionApiV1SessionExtendPostError = ExtendSessionApiV1SessionExtendPostErrors[keyof ExtendSessionApiV1SessionExtendPostErrors];

export type ExtendSessionApiV1SessionExtendPostResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type LogoutApiV1SessionLogoutDeleteData = {
    body?: never;
    headers?: {
        /**
         * X-Session-Id
         */
        'x-session-id'?: string | null;
    };
    path?: never;
    query?: never;
    url: '/api/v1/session/logout';
};

export type LogoutApiV1SessionLogoutDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type LogoutApiV1SessionLogoutDeleteError = LogoutApiV1SessionLogoutDeleteErrors[keyof LogoutApiV1SessionLogoutDeleteErrors];

export type LogoutApiV1SessionLogoutDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetPermissionTreeApiV1PermissionsTreeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/permissions/tree';
};

export type GetPermissionTreeApiV1PermissionsTreeGetResponses = {
    /**
     * Response Get Permission Tree Api V1 Permissions Tree Get
     * Successful Response
     */
    200: Array<PermissionTreeDto>;
};

export type GetPermissionTreeApiV1PermissionsTreeGetResponse = GetPermissionTreeApiV1PermissionsTreeGetResponses[keyof GetPermissionTreeApiV1PermissionsTreeGetResponses];

export type GetAllPermissionsApiV1PermissionsListGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/permissions/list';
};

export type GetAllPermissionsApiV1PermissionsListGetResponses = {
    /**
     * Successful Response
     */
    200: PermissionListDto;
};

export type GetAllPermissionsApiV1PermissionsListGetResponse = GetAllPermissionsApiV1PermissionsListGetResponses[keyof GetAllPermissionsApiV1PermissionsListGetResponses];

export type GetAllRolesApiV1RolesGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/roles/';
};

export type GetAllRolesApiV1RolesGetResponses = {
    /**
     * Successful Response
     */
    200: RoleListDto;
};

export type GetAllRolesApiV1RolesGetResponse = GetAllRolesApiV1RolesGetResponses[keyof GetAllRolesApiV1RolesGetResponses];

export type CreateRoleApiV1RolesPostData = {
    body: RoleCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/roles/';
};

export type CreateRoleApiV1RolesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateRoleApiV1RolesPostError = CreateRoleApiV1RolesPostErrors[keyof CreateRoleApiV1RolesPostErrors];

export type CreateRoleApiV1RolesPostResponses = {
    /**
     * Successful Response
     */
    201: RoleResponseDto;
};

export type CreateRoleApiV1RolesPostResponse = CreateRoleApiV1RolesPostResponses[keyof CreateRoleApiV1RolesPostResponses];

export type GetActiveRolesApiV1RolesActiveGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/roles/active';
};

export type GetActiveRolesApiV1RolesActiveGetResponses = {
    /**
     * Successful Response
     */
    200: RoleListDto;
};

export type GetActiveRolesApiV1RolesActiveGetResponse = GetActiveRolesApiV1RolesActiveGetResponses[keyof GetActiveRolesApiV1RolesActiveGetResponses];

export type GetRolesSummaryApiV1RolesSummaryGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/roles/summary';
};

export type GetRolesSummaryApiV1RolesSummaryGetResponses = {
    /**
     * Response Get Roles Summary Api V1 Roles Summary Get
     * Successful Response
     */
    200: Array<RoleSummaryDto>;
};

export type GetRolesSummaryApiV1RolesSummaryGetResponse = GetRolesSummaryApiV1RolesSummaryGetResponses[keyof GetRolesSummaryApiV1RolesSummaryGetResponses];

export type DeleteRoleApiV1RolesRoleIdDeleteData = {
    body?: never;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}';
};

export type DeleteRoleApiV1RolesRoleIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteRoleApiV1RolesRoleIdDeleteError = DeleteRoleApiV1RolesRoleIdDeleteErrors[keyof DeleteRoleApiV1RolesRoleIdDeleteErrors];

export type DeleteRoleApiV1RolesRoleIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteRoleApiV1RolesRoleIdDeleteResponse = DeleteRoleApiV1RolesRoleIdDeleteResponses[keyof DeleteRoleApiV1RolesRoleIdDeleteResponses];

export type GetRoleByIdApiV1RolesRoleIdGetData = {
    body?: never;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}';
};

export type GetRoleByIdApiV1RolesRoleIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetRoleByIdApiV1RolesRoleIdGetError = GetRoleByIdApiV1RolesRoleIdGetErrors[keyof GetRoleByIdApiV1RolesRoleIdGetErrors];

export type GetRoleByIdApiV1RolesRoleIdGetResponses = {
    /**
     * Successful Response
     */
    200: RoleResponseDto;
};

export type GetRoleByIdApiV1RolesRoleIdGetResponse = GetRoleByIdApiV1RolesRoleIdGetResponses[keyof GetRoleByIdApiV1RolesRoleIdGetResponses];

export type UpdateRoleApiV1RolesRoleIdPutData = {
    body: RoleUpdateDto;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}';
};

export type UpdateRoleApiV1RolesRoleIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateRoleApiV1RolesRoleIdPutError = UpdateRoleApiV1RolesRoleIdPutErrors[keyof UpdateRoleApiV1RolesRoleIdPutErrors];

export type UpdateRoleApiV1RolesRoleIdPutResponses = {
    /**
     * Successful Response
     */
    200: RoleResponseDto;
};

export type UpdateRoleApiV1RolesRoleIdPutResponse = UpdateRoleApiV1RolesRoleIdPutResponses[keyof UpdateRoleApiV1RolesRoleIdPutResponses];

export type RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteData = {
    body: RolePermissionRemoveDto;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}/permissions';
};

export type RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteError = RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteErrors[keyof RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteErrors];

export type RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponses = {
    /**
     * Successful Response
     */
    200: RoleResponseDto;
};

export type RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponse = RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponses[keyof RemovePermissionsFromRoleApiV1RolesRoleIdPermissionsDeleteResponses];

export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetData = {
    body?: never;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}/permissions';
};

export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetError = GetRolePermissionsApiV1RolesRoleIdPermissionsGetErrors[keyof GetRolePermissionsApiV1RolesRoleIdPermissionsGetErrors];

export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponses = {
    /**
     * Response Get Role Permissions Api V1 Roles  Role Id  Permissions Get
     * Successful Response
     */
    200: Array<PermissionResponseDto>;
};

export type GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponse = GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponses[keyof GetRolePermissionsApiV1RolesRoleIdPermissionsGetResponses];

export type AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostData = {
    body: RolePermissionAssignDto;
    path: {
        /**
         * Role Id
         */
        role_id: number;
    };
    query?: never;
    url: '/api/v1/roles/{role_id}/permissions';
};

export type AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostError = AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostErrors[keyof AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostErrors];

export type AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponses = {
    /**
     * Successful Response
     */
    200: RoleResponseDto;
};

export type AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponse = AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponses[keyof AssignPermissionsToRoleApiV1RolesRoleIdPermissionsPostResponses];

export type GetFactoryUserListApiV1UserManagementUserListGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/user-list';
};

export type GetFactoryUserListApiV1UserManagementUserListGetResponses = {
    /**
     * Successful Response
     */
    200: FactoryUserListDto;
};

export type GetFactoryUserListApiV1UserManagementUserListGetResponse = GetFactoryUserListApiV1UserManagementUserListGetResponses[keyof GetFactoryUserListApiV1UserManagementUserListGetResponses];

export type GetAvailableUsersApiV1UserManagementAvailableUsersGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by username, email, or full name
         */
        search_term?: string | null;
        /**
         * Role Id
         * Filter by system role
         */
        role_id?: number | null;
        /**
         * Is Active
         * Filter by active status
         */
        is_active?: boolean | null;
    };
    url: '/api/v1/user-management/available-users';
};

export type GetAvailableUsersApiV1UserManagementAvailableUsersGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAvailableUsersApiV1UserManagementAvailableUsersGetError = GetAvailableUsersApiV1UserManagementAvailableUsersGetErrors[keyof GetAvailableUsersApiV1UserManagementAvailableUsersGetErrors];

export type GetAvailableUsersApiV1UserManagementAvailableUsersGetResponses = {
    /**
     * Successful Response
     */
    200: AvailableUsersDto;
};

export type GetAvailableUsersApiV1UserManagementAvailableUsersGetResponse = GetAvailableUsersApiV1UserManagementAvailableUsersGetResponses[keyof GetAvailableUsersApiV1UserManagementAvailableUsersGetResponses];

export type AddUsersToFactoryApiV1UserManagementAddUsersPostData = {
    body: AddUsersToFactoryDto;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/add-users';
};

export type AddUsersToFactoryApiV1UserManagementAddUsersPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type AddUsersToFactoryApiV1UserManagementAddUsersPostError = AddUsersToFactoryApiV1UserManagementAddUsersPostErrors[keyof AddUsersToFactoryApiV1UserManagementAddUsersPostErrors];

export type AddUsersToFactoryApiV1UserManagementAddUsersPostResponses = {
    /**
     * Response Add Users To Factory Api V1 User Management Add Users Post
     * Successful Response
     */
    200: Array<UserOperationResultDto>;
};

export type AddUsersToFactoryApiV1UserManagementAddUsersPostResponse = AddUsersToFactoryApiV1UserManagementAddUsersPostResponses[keyof AddUsersToFactoryApiV1UserManagementAddUsersPostResponses];

export type BindUserRoleApiV1UserManagementBindRolesPostData = {
    body: BindUserRoleDto;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/bind-roles';
};

export type BindUserRoleApiV1UserManagementBindRolesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type BindUserRoleApiV1UserManagementBindRolesPostError = BindUserRoleApiV1UserManagementBindRolesPostErrors[keyof BindUserRoleApiV1UserManagementBindRolesPostErrors];

export type BindUserRoleApiV1UserManagementBindRolesPostResponses = {
    /**
     * Successful Response
     */
    200: UserOperationResultDto;
};

export type BindUserRoleApiV1UserManagementBindRolesPostResponse = BindUserRoleApiV1UserManagementBindRolesPostResponses[keyof BindUserRoleApiV1UserManagementBindRolesPostResponses];

export type SuspendUserInFactoryApiV1UserManagementSuspendPostData = {
    body: SuspendUserDto;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/suspend';
};

export type SuspendUserInFactoryApiV1UserManagementSuspendPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SuspendUserInFactoryApiV1UserManagementSuspendPostError = SuspendUserInFactoryApiV1UserManagementSuspendPostErrors[keyof SuspendUserInFactoryApiV1UserManagementSuspendPostErrors];

export type SuspendUserInFactoryApiV1UserManagementSuspendPostResponses = {
    /**
     * Successful Response
     */
    200: UserOperationResultDto;
};

export type SuspendUserInFactoryApiV1UserManagementSuspendPostResponse = SuspendUserInFactoryApiV1UserManagementSuspendPostResponses[keyof SuspendUserInFactoryApiV1UserManagementSuspendPostResponses];

export type RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteData = {
    body: RemoveUserFromFactoryDto;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/remove-user';
};

export type RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteError = RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteErrors[keyof RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteErrors];

export type RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponses = {
    /**
     * Successful Response
     */
    200: UserOperationResultDto;
};

export type RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponse = RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponses[keyof RemoveUserFromFactoryApiV1UserManagementRemoveUserDeleteResponses];

export type CreateUserWithFactoryApiV1UserManagementCreateUserPostData = {
    body: CreateUserWithFactoryDto;
    path?: never;
    query?: never;
    url: '/api/v1/user-management/create-user';
};

export type CreateUserWithFactoryApiV1UserManagementCreateUserPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateUserWithFactoryApiV1UserManagementCreateUserPostError = CreateUserWithFactoryApiV1UserManagementCreateUserPostErrors[keyof CreateUserWithFactoryApiV1UserManagementCreateUserPostErrors];

export type CreateUserWithFactoryApiV1UserManagementCreateUserPostResponses = {
    /**
     * Successful Response
     */
    200: AddUserWithFactoryResponseDto;
};

export type CreateUserWithFactoryApiV1UserManagementCreateUserPostResponse = CreateUserWithFactoryApiV1UserManagementCreateUserPostResponses[keyof CreateUserWithFactoryApiV1UserManagementCreateUserPostResponses];

export type GetAllSkillsApiV1SkillsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by code, name, or description
         */
        search_term?: string | null;
        /**
         * Category
         * Filter by category
         */
        category?: string | null;
        /**
         * Is Active
         * Filter by active status
         */
        is_active?: boolean | null;
    };
    url: '/api/v1/skills/';
};

export type GetAllSkillsApiV1SkillsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAllSkillsApiV1SkillsGetError = GetAllSkillsApiV1SkillsGetErrors[keyof GetAllSkillsApiV1SkillsGetErrors];

export type GetAllSkillsApiV1SkillsGetResponses = {
    /**
     * Successful Response
     */
    200: SkillListDto;
};

export type GetAllSkillsApiV1SkillsGetResponse = GetAllSkillsApiV1SkillsGetResponses[keyof GetAllSkillsApiV1SkillsGetResponses];

export type CreateSkillApiV1SkillsPostData = {
    body: SkillCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/skills/';
};

export type CreateSkillApiV1SkillsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateSkillApiV1SkillsPostError = CreateSkillApiV1SkillsPostErrors[keyof CreateSkillApiV1SkillsPostErrors];

export type CreateSkillApiV1SkillsPostResponses = {
    /**
     * Successful Response
     */
    201: SkillResponseDto;
};

export type CreateSkillApiV1SkillsPostResponse = CreateSkillApiV1SkillsPostResponses[keyof CreateSkillApiV1SkillsPostResponses];

export type GetActiveSkillsApiV1SkillsActiveGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/skills/active';
};

export type GetActiveSkillsApiV1SkillsActiveGetResponses = {
    /**
     * Successful Response
     */
    200: SkillListDto;
};

export type GetActiveSkillsApiV1SkillsActiveGetResponse = GetActiveSkillsApiV1SkillsActiveGetResponses[keyof GetActiveSkillsApiV1SkillsActiveGetResponses];

export type DeleteSkillApiV1SkillsSkillIdDeleteData = {
    body?: never;
    path: {
        /**
         * Skill Id
         */
        skill_id: number;
    };
    query?: never;
    url: '/api/v1/skills/{skill_id}';
};

export type DeleteSkillApiV1SkillsSkillIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteSkillApiV1SkillsSkillIdDeleteError = DeleteSkillApiV1SkillsSkillIdDeleteErrors[keyof DeleteSkillApiV1SkillsSkillIdDeleteErrors];

export type DeleteSkillApiV1SkillsSkillIdDeleteResponses = {
    /**
     * Successful Response
     */
    204: void;
};

export type DeleteSkillApiV1SkillsSkillIdDeleteResponse = DeleteSkillApiV1SkillsSkillIdDeleteResponses[keyof DeleteSkillApiV1SkillsSkillIdDeleteResponses];

export type GetSkillByIdApiV1SkillsSkillIdGetData = {
    body?: never;
    path: {
        /**
         * Skill Id
         */
        skill_id: number;
    };
    query?: never;
    url: '/api/v1/skills/{skill_id}';
};

export type GetSkillByIdApiV1SkillsSkillIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetSkillByIdApiV1SkillsSkillIdGetError = GetSkillByIdApiV1SkillsSkillIdGetErrors[keyof GetSkillByIdApiV1SkillsSkillIdGetErrors];

export type GetSkillByIdApiV1SkillsSkillIdGetResponses = {
    /**
     * Successful Response
     */
    200: SkillResponseDto;
};

export type GetSkillByIdApiV1SkillsSkillIdGetResponse = GetSkillByIdApiV1SkillsSkillIdGetResponses[keyof GetSkillByIdApiV1SkillsSkillIdGetResponses];

export type UpdateSkillApiV1SkillsSkillIdPutData = {
    body: SkillUpdateDto;
    path: {
        /**
         * Skill Id
         */
        skill_id: number;
    };
    query?: never;
    url: '/api/v1/skills/{skill_id}';
};

export type UpdateSkillApiV1SkillsSkillIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateSkillApiV1SkillsSkillIdPutError = UpdateSkillApiV1SkillsSkillIdPutErrors[keyof UpdateSkillApiV1SkillsSkillIdPutErrors];

export type UpdateSkillApiV1SkillsSkillIdPutResponses = {
    /**
     * Successful Response
     */
    200: SkillResponseDto;
};

export type UpdateSkillApiV1SkillsSkillIdPutResponse = UpdateSkillApiV1SkillsSkillIdPutResponses[keyof UpdateSkillApiV1SkillsSkillIdPutResponses];

export type GetUserSkillsApiV1SkillsUserUserIdSkillsGetData = {
    body?: never;
    path: {
        /**
         * User Id
         */
        user_id: number;
    };
    query?: never;
    url: '/api/v1/skills/user/{user_id}/skills';
};

export type GetUserSkillsApiV1SkillsUserUserIdSkillsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetUserSkillsApiV1SkillsUserUserIdSkillsGetError = GetUserSkillsApiV1SkillsUserUserIdSkillsGetErrors[keyof GetUserSkillsApiV1SkillsUserUserIdSkillsGetErrors];

export type GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponses = {
    /**
     * Successful Response
     */
    200: UserSkillsDto;
};

export type GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponse = GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponses[keyof GetUserSkillsApiV1SkillsUserUserIdSkillsGetResponses];

export type AssignSkillsToUserApiV1SkillsAssignPostData = {
    body: AssignSkillsDto;
    path?: never;
    query?: never;
    url: '/api/v1/skills/assign';
};

export type AssignSkillsToUserApiV1SkillsAssignPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type AssignSkillsToUserApiV1SkillsAssignPostError = AssignSkillsToUserApiV1SkillsAssignPostErrors[keyof AssignSkillsToUserApiV1SkillsAssignPostErrors];

export type AssignSkillsToUserApiV1SkillsAssignPostResponses = {
    /**
     * Response Assign Skills To User Api V1 Skills Assign Post
     * Successful Response
     */
    200: Array<SkillOperationResultDto>;
};

export type AssignSkillsToUserApiV1SkillsAssignPostResponse = AssignSkillsToUserApiV1SkillsAssignPostResponses[keyof AssignSkillsToUserApiV1SkillsAssignPostResponses];

export type ModifySkillProficiencyApiV1SkillsModifyProficiencyPutData = {
    body: UpdateSkillProficiencyDto;
    path?: never;
    query?: never;
    url: '/api/v1/skills/modify-proficiency';
};

export type ModifySkillProficiencyApiV1SkillsModifyProficiencyPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ModifySkillProficiencyApiV1SkillsModifyProficiencyPutError = ModifySkillProficiencyApiV1SkillsModifyProficiencyPutErrors[keyof ModifySkillProficiencyApiV1SkillsModifyProficiencyPutErrors];

export type ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponses = {
    /**
     * Successful Response
     */
    200: SkillOperationResultDto;
};

export type ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponse = ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponses[keyof ModifySkillProficiencyApiV1SkillsModifyProficiencyPutResponses];

export type CertifyUserSkillApiV1SkillsCertifyPostData = {
    body: CertifySkillDto;
    path?: never;
    query?: never;
    url: '/api/v1/skills/certify';
};

export type CertifyUserSkillApiV1SkillsCertifyPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CertifyUserSkillApiV1SkillsCertifyPostError = CertifyUserSkillApiV1SkillsCertifyPostErrors[keyof CertifyUserSkillApiV1SkillsCertifyPostErrors];

export type CertifyUserSkillApiV1SkillsCertifyPostResponses = {
    /**
     * Successful Response
     */
    200: SkillOperationResultDto;
};

export type CertifyUserSkillApiV1SkillsCertifyPostResponse = CertifyUserSkillApiV1SkillsCertifyPostResponses[keyof CertifyUserSkillApiV1SkillsCertifyPostResponses];

export type RemoveUserSkillApiV1SkillsRemoveDeleteData = {
    body: RemoveSkillDto;
    path?: never;
    query?: never;
    url: '/api/v1/skills/remove';
};

export type RemoveUserSkillApiV1SkillsRemoveDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RemoveUserSkillApiV1SkillsRemoveDeleteError = RemoveUserSkillApiV1SkillsRemoveDeleteErrors[keyof RemoveUserSkillApiV1SkillsRemoveDeleteErrors];

export type RemoveUserSkillApiV1SkillsRemoveDeleteResponses = {
    /**
     * Successful Response
     */
    200: SkillOperationResultDto;
};

export type RemoveUserSkillApiV1SkillsRemoveDeleteResponse = RemoveUserSkillApiV1SkillsRemoveDeleteResponses[keyof RemoveUserSkillApiV1SkillsRemoveDeleteResponses];

export type GetAllCraftsApiV1CraftsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by code, name, or description
         */
        search_term?: string | null;
        /**
         * Enabled
         * Filter by enabled status
         */
        enabled?: boolean | null;
        /**
         * Min Priority
         * Minimum priority
         */
        min_priority?: number | null;
        /**
         * Max Priority
         * Maximum priority
         */
        max_priority?: number | null;
    };
    url: '/api/v1/crafts/';
};

export type GetAllCraftsApiV1CraftsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAllCraftsApiV1CraftsGetError = GetAllCraftsApiV1CraftsGetErrors[keyof GetAllCraftsApiV1CraftsGetErrors];

export type GetAllCraftsApiV1CraftsGetResponses = {
    /**
     * Successful Response
     */
    200: CraftListDto;
};

export type GetAllCraftsApiV1CraftsGetResponse = GetAllCraftsApiV1CraftsGetResponses[keyof GetAllCraftsApiV1CraftsGetResponses];

export type CreateCraftApiV1CraftsPostData = {
    body: CraftCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/crafts/';
};

export type CreateCraftApiV1CraftsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateCraftApiV1CraftsPostError = CreateCraftApiV1CraftsPostErrors[keyof CreateCraftApiV1CraftsPostErrors];

export type CreateCraftApiV1CraftsPostResponses = {
    /**
     * Successful Response
     */
    200: CraftResponseDto;
};

export type CreateCraftApiV1CraftsPostResponse = CreateCraftApiV1CraftsPostResponses[keyof CreateCraftApiV1CraftsPostResponses];

export type GetEnabledCraftsApiV1CraftsEnabledGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/crafts/enabled';
};

export type GetEnabledCraftsApiV1CraftsEnabledGetResponses = {
    /**
     * Successful Response
     */
    200: CraftListDto;
};

export type GetEnabledCraftsApiV1CraftsEnabledGetResponse = GetEnabledCraftsApiV1CraftsEnabledGetResponses[keyof GetEnabledCraftsApiV1CraftsEnabledGetResponses];

export type DeleteCraftApiV1CraftsCraftIdDeleteData = {
    body?: never;
    path: {
        /**
         * Craft Id
         */
        craft_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/{craft_id}';
};

export type DeleteCraftApiV1CraftsCraftIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteCraftApiV1CraftsCraftIdDeleteError = DeleteCraftApiV1CraftsCraftIdDeleteErrors[keyof DeleteCraftApiV1CraftsCraftIdDeleteErrors];

export type DeleteCraftApiV1CraftsCraftIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetCraftByIdApiV1CraftsCraftIdGetData = {
    body?: never;
    path: {
        /**
         * Craft Id
         */
        craft_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/{craft_id}';
};

export type GetCraftByIdApiV1CraftsCraftIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCraftByIdApiV1CraftsCraftIdGetError = GetCraftByIdApiV1CraftsCraftIdGetErrors[keyof GetCraftByIdApiV1CraftsCraftIdGetErrors];

export type GetCraftByIdApiV1CraftsCraftIdGetResponses = {
    /**
     * Successful Response
     */
    200: CraftResponseDto;
};

export type GetCraftByIdApiV1CraftsCraftIdGetResponse = GetCraftByIdApiV1CraftsCraftIdGetResponses[keyof GetCraftByIdApiV1CraftsCraftIdGetResponses];

export type UpdateCraftApiV1CraftsCraftIdPutData = {
    body: CraftUpdateDto;
    path: {
        /**
         * Craft Id
         */
        craft_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/{craft_id}';
};

export type UpdateCraftApiV1CraftsCraftIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateCraftApiV1CraftsCraftIdPutError = UpdateCraftApiV1CraftsCraftIdPutErrors[keyof UpdateCraftApiV1CraftsCraftIdPutErrors];

export type UpdateCraftApiV1CraftsCraftIdPutResponses = {
    /**
     * Successful Response
     */
    200: CraftResponseDto;
};

export type UpdateCraftApiV1CraftsCraftIdPutResponse = UpdateCraftApiV1CraftsCraftIdPutResponses[keyof UpdateCraftApiV1CraftsCraftIdPutResponses];

export type GetCraftByCodeApiV1CraftsCodeCraftCodeGetData = {
    body?: never;
    path: {
        /**
         * Craft Code
         */
        craft_code: string;
    };
    query?: never;
    url: '/api/v1/crafts/code/{craft_code}';
};

export type GetCraftByCodeApiV1CraftsCodeCraftCodeGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCraftByCodeApiV1CraftsCodeCraftCodeGetError = GetCraftByCodeApiV1CraftsCodeCraftCodeGetErrors[keyof GetCraftByCodeApiV1CraftsCodeCraftCodeGetErrors];

export type GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponses = {
    /**
     * Successful Response
     */
    200: CraftWithRoutesDto;
};

export type GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponse = GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponses[keyof GetCraftByCodeApiV1CraftsCodeCraftCodeGetResponses];

export type CreateCraftRouteApiV1CraftsRoutesPostData = {
    body: CraftRouteCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/crafts/routes';
};

export type CreateCraftRouteApiV1CraftsRoutesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateCraftRouteApiV1CraftsRoutesPostError = CreateCraftRouteApiV1CraftsRoutesPostErrors[keyof CreateCraftRouteApiV1CraftsRoutesPostErrors];

export type CreateCraftRouteApiV1CraftsRoutesPostResponses = {
    /**
     * Successful Response
     */
    200: CraftRouteResponseDto;
};

export type CreateCraftRouteApiV1CraftsRoutesPostResponse = CreateCraftRouteApiV1CraftsRoutesPostResponses[keyof CreateCraftRouteApiV1CraftsRoutesPostResponses];

export type CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostData = {
    body: BulkCraftRouteCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/crafts/routes/bulk';
};

export type CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostError = CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostErrors[keyof CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostErrors];

export type CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponses = {
    /**
     * Successful Response
     */
    200: BulkCraftRouteOperationResultDto;
};

export type CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponse = CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponses[keyof CreateBulkCraftRoutesApiV1CraftsRoutesBulkPostResponses];

export type GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetData = {
    body?: never;
    path: {
        /**
         * Craft Code
         */
        craft_code: string;
    };
    query?: never;
    url: '/api/v1/crafts/routes/craft/{craft_code}';
};

export type GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetError = GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetErrors[keyof GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetErrors];

export type GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponses = {
    /**
     * Successful Response
     */
    200: CraftRouteListDto;
};

export type GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponse = GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponses[keyof GetCraftRoutesApiV1CraftsRoutesCraftCraftCodeGetResponses];

export type GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetData = {
    body?: never;
    path: {
        /**
         * Skill Code
         */
        skill_code: string;
    };
    query?: never;
    url: '/api/v1/crafts/routes/skill/{skill_code}';
};

export type GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetError = GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetErrors[keyof GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetErrors];

export type GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponses = {
    /**
     * Successful Response
     */
    200: CraftRouteListDto;
};

export type GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponse = GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponses[keyof GetSkillRoutesApiV1CraftsRoutesSkillSkillCodeGetResponses];

export type DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteData = {
    body?: never;
    path: {
        /**
         * Route Id
         */
        route_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/routes/{route_id}';
};

export type DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteError = DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteErrors[keyof DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteErrors];

export type DeleteCraftRouteApiV1CraftsRoutesRouteIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type UpdateCraftRouteApiV1CraftsRoutesRouteIdPutData = {
    body: CraftRouteUpdateDto;
    path: {
        /**
         * Route Id
         */
        route_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/routes/{route_id}';
};

export type UpdateCraftRouteApiV1CraftsRoutesRouteIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateCraftRouteApiV1CraftsRoutesRouteIdPutError = UpdateCraftRouteApiV1CraftsRoutesRouteIdPutErrors[keyof UpdateCraftRouteApiV1CraftsRoutesRouteIdPutErrors];

export type UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponses = {
    /**
     * Successful Response
     */
    200: CraftRouteResponseDto;
};

export type UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponse = UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponses[keyof UpdateCraftRouteApiV1CraftsRoutesRouteIdPutResponses];

export type ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostData = {
    /**
     * Route Orders
     */
    body: Array<{
        [key: string]: unknown;
    }>;
    path: {
        /**
         * Craft Code
         */
        craft_code: string;
    };
    query?: never;
    url: '/api/v1/crafts/routes/reorder/{craft_code}';
};

export type ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostError = ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostErrors[keyof ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostErrors];

export type ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponses = {
    /**
     * Successful Response
     */
    200: CraftOperationResultDto;
};

export type ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponse = ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponses[keyof ReorderCraftRoutesApiV1CraftsRoutesReorderCraftCodePostResponses];

export type GetDepartmentsApiV1DepartmentsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Skip
         * Skip items
         */
        skip?: number;
        /**
         * Limit
         * Limit items
         */
        limit?: number;
    };
    url: '/api/v1/departments/';
};

export type GetDepartmentsApiV1DepartmentsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDepartmentsApiV1DepartmentsGetError = GetDepartmentsApiV1DepartmentsGetErrors[keyof GetDepartmentsApiV1DepartmentsGetErrors];

export type GetDepartmentsApiV1DepartmentsGetResponses = {
    /**
     * Successful Response
     */
    200: DepartmentListDto;
};

export type GetDepartmentsApiV1DepartmentsGetResponse = GetDepartmentsApiV1DepartmentsGetResponses[keyof GetDepartmentsApiV1DepartmentsGetResponses];

export type CreateDepartmentApiV1DepartmentsPostData = {
    body: DepartmentCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/departments/';
};

export type CreateDepartmentApiV1DepartmentsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateDepartmentApiV1DepartmentsPostError = CreateDepartmentApiV1DepartmentsPostErrors[keyof CreateDepartmentApiV1DepartmentsPostErrors];

export type CreateDepartmentApiV1DepartmentsPostResponses = {
    /**
     * Successful Response
     */
    201: DepartmentResponseDto;
};

export type CreateDepartmentApiV1DepartmentsPostResponse = CreateDepartmentApiV1DepartmentsPostResponses[keyof CreateDepartmentApiV1DepartmentsPostResponses];

export type SearchDepartmentsApiV1DepartmentsSearchGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by name or code
         */
        search_term?: string | null;
        /**
         * Is Active
         * Filter by active status
         */
        is_active?: boolean | null;
        /**
         * Manager Name
         * Filter by manager name
         */
        manager_name?: string | null;
        /**
         * Skip
         * Skip items
         */
        skip?: number;
        /**
         * Limit
         * Limit items
         */
        limit?: number;
    };
    url: '/api/v1/departments/search';
};

export type SearchDepartmentsApiV1DepartmentsSearchGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SearchDepartmentsApiV1DepartmentsSearchGetError = SearchDepartmentsApiV1DepartmentsSearchGetErrors[keyof SearchDepartmentsApiV1DepartmentsSearchGetErrors];

export type SearchDepartmentsApiV1DepartmentsSearchGetResponses = {
    /**
     * Successful Response
     */
    200: DepartmentListDto;
};

export type SearchDepartmentsApiV1DepartmentsSearchGetResponse = SearchDepartmentsApiV1DepartmentsSearchGetResponses[keyof SearchDepartmentsApiV1DepartmentsSearchGetResponses];

export type DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteData = {
    body?: never;
    path: {
        /**
         * Department Id
         */
        department_id: number;
    };
    query?: never;
    url: '/api/v1/departments/{department_id}';
};

export type DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteError = DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteErrors[keyof DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteErrors];

export type DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: DepartmentOperationResultDto;
};

export type DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponse = DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponses[keyof DeleteDepartmentApiV1DepartmentsDepartmentIdDeleteResponses];

export type GetDepartmentByIdApiV1DepartmentsDepartmentIdGetData = {
    body?: never;
    path: {
        /**
         * Department Id
         */
        department_id: number;
    };
    query?: never;
    url: '/api/v1/departments/{department_id}';
};

export type GetDepartmentByIdApiV1DepartmentsDepartmentIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDepartmentByIdApiV1DepartmentsDepartmentIdGetError = GetDepartmentByIdApiV1DepartmentsDepartmentIdGetErrors[keyof GetDepartmentByIdApiV1DepartmentsDepartmentIdGetErrors];

export type GetDepartmentByIdApiV1DepartmentsDepartmentIdGetResponses = {
    /**
     * Successful Response
     */
    200: DepartmentResponseDto;
};

export type GetDepartmentByIdApiV1DepartmentsDepartmentIdGetResponse = GetDepartmentByIdApiV1DepartmentsDepartmentIdGetResponses[keyof GetDepartmentByIdApiV1DepartmentsDepartmentIdGetResponses];

export type UpdateDepartmentApiV1DepartmentsDepartmentIdPutData = {
    body: DepartmentUpdateDto;
    path: {
        /**
         * Department Id
         */
        department_id: number;
    };
    query?: never;
    url: '/api/v1/departments/{department_id}';
};

export type UpdateDepartmentApiV1DepartmentsDepartmentIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateDepartmentApiV1DepartmentsDepartmentIdPutError = UpdateDepartmentApiV1DepartmentsDepartmentIdPutErrors[keyof UpdateDepartmentApiV1DepartmentsDepartmentIdPutErrors];

export type UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponses = {
    /**
     * Successful Response
     */
    200: DepartmentResponseDto;
};

export type UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponse = UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponses[keyof UpdateDepartmentApiV1DepartmentsDepartmentIdPutResponses];

export type GetAllOrdersApiV1OrdersGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by order_no, skc_no, etc.
         */
        search_term?: string | null;
        /**
         * Status Filter
         * Filter by order status
         */
        status_filter?: string | null;
        /**
         * Owner User Id
         * Filter by owner user ID
         */
        owner_user_id?: number | null;
        /**
         * Current Craft
         * Filter by current craft
         */
        current_craft?: string | null;
        /**
         * Skip
         * Skip items
         */
        skip?: number;
        /**
         * Limit
         * Limit items
         */
        limit?: number;
    };
    url: '/api/v1/orders/';
};

export type GetAllOrdersApiV1OrdersGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAllOrdersApiV1OrdersGetError = GetAllOrdersApiV1OrdersGetErrors[keyof GetAllOrdersApiV1OrdersGetErrors];

export type GetAllOrdersApiV1OrdersGetResponses = {
    /**
     * Successful Response
     */
    200: OrderListDto;
};

export type GetAllOrdersApiV1OrdersGetResponse = GetAllOrdersApiV1OrdersGetResponses[keyof GetAllOrdersApiV1OrdersGetResponses];

export type CreateOrderApiV1OrdersPostData = {
    body: OrderCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/orders/';
};

export type CreateOrderApiV1OrdersPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrderApiV1OrdersPostError = CreateOrderApiV1OrdersPostErrors[keyof CreateOrderApiV1OrdersPostErrors];

export type CreateOrderApiV1OrdersPostResponses = {
    /**
     * Successful Response
     */
    200: OrderDetailResponseDto;
};

export type CreateOrderApiV1OrdersPostResponse = CreateOrderApiV1OrdersPostResponses[keyof CreateOrderApiV1OrdersPostResponses];

export type GetOrderByOrderNoMainApiV1OrdersOrderNoGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}';
};

export type GetOrderByOrderNoMainApiV1OrdersOrderNoGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderByOrderNoMainApiV1OrdersOrderNoGetError = GetOrderByOrderNoMainApiV1OrdersOrderNoGetErrors[keyof GetOrderByOrderNoMainApiV1OrdersOrderNoGetErrors];

export type GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponses = {
    /**
     * Successful Response
     */
    200: OrderDetailResponseDto;
};

export type GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponse = GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponses[keyof GetOrderByOrderNoMainApiV1OrdersOrderNoGetResponses];

export type DeleteOrderApiV1OrdersOrderIdDeleteData = {
    body?: never;
    path: {
        /**
         * Order Id
         */
        order_id: number;
    };
    query?: never;
    url: '/api/v1/orders/{order_id}';
};

export type DeleteOrderApiV1OrdersOrderIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrderApiV1OrdersOrderIdDeleteError = DeleteOrderApiV1OrdersOrderIdDeleteErrors[keyof DeleteOrderApiV1OrdersOrderIdDeleteErrors];

export type DeleteOrderApiV1OrdersOrderIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type UpdateOrderApiV1OrdersOrderIdPutData = {
    body: OrderUpdateDto;
    path: {
        /**
         * Order Id
         */
        order_id: number;
    };
    query?: never;
    url: '/api/v1/orders/{order_id}';
};

export type UpdateOrderApiV1OrdersOrderIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderApiV1OrdersOrderIdPutError = UpdateOrderApiV1OrdersOrderIdPutErrors[keyof UpdateOrderApiV1OrdersOrderIdPutErrors];

export type UpdateOrderApiV1OrdersOrderIdPutResponses = {
    /**
     * Successful Response
     */
    200: OrderResponseDto;
};

export type UpdateOrderApiV1OrdersOrderIdPutResponse = UpdateOrderApiV1OrdersOrderIdPutResponses[keyof UpdateOrderApiV1OrdersOrderIdPutResponses];

export type UpdateOrderStatusApiV1OrdersOrderIdStatusPutData = {
    body: OrderStatusUpdateDto;
    path: {
        /**
         * Order Id
         */
        order_id: number;
    };
    query?: never;
    url: '/api/v1/orders/{order_id}/status';
};

export type UpdateOrderStatusApiV1OrdersOrderIdStatusPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderStatusApiV1OrdersOrderIdStatusPutError = UpdateOrderStatusApiV1OrdersOrderIdStatusPutErrors[keyof UpdateOrderStatusApiV1OrdersOrderIdStatusPutErrors];

export type UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponses = {
    /**
     * Successful Response
     */
    200: OrderOperationResultDto;
};

export type UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponse = UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponses[keyof UpdateOrderStatusApiV1OrdersOrderIdStatusPutResponses];

export type StartOrderApiV1OrdersOrderNoStartPostData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}/start';
};

export type StartOrderApiV1OrdersOrderNoStartPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type StartOrderApiV1OrdersOrderNoStartPostError = StartOrderApiV1OrdersOrderNoStartPostErrors[keyof StartOrderApiV1OrdersOrderNoStartPostErrors];

export type StartOrderApiV1OrdersOrderNoStartPostResponses = {
    /**
     * Successful Response
     */
    200: OrderOperationResultDto;
};

export type StartOrderApiV1OrdersOrderNoStartPostResponse = StartOrderApiV1OrdersOrderNoStartPostResponses[keyof StartOrderApiV1OrdersOrderNoStartPostResponses];

export type UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutData = {
    body: OrderCraftProgressDto;
    path: {
        /**
         * Order Id
         */
        order_id: number;
    };
    query?: never;
    url: '/api/v1/orders/{order_id}/craft-progress';
};

export type UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutError = UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutErrors[keyof UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutErrors];

export type UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponses = {
    /**
     * Successful Response
     */
    200: OrderOperationResultDto;
};

export type UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponse = UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponses[keyof UpdateCraftProgressApiV1OrdersOrderIdCraftProgressPutResponses];

export type UpdateOrderAmountApiV1OrdersOrderIdAmountPutData = {
    body: OrderAmountUpdateDto;
    path: {
        /**
         * Order Id
         */
        order_id: number;
    };
    query?: never;
    url: '/api/v1/orders/{order_id}/amount';
};

export type UpdateOrderAmountApiV1OrdersOrderIdAmountPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderAmountApiV1OrdersOrderIdAmountPutError = UpdateOrderAmountApiV1OrdersOrderIdAmountPutErrors[keyof UpdateOrderAmountApiV1OrdersOrderIdAmountPutErrors];

export type UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponses = {
    /**
     * Successful Response
     */
    200: OrderOperationResultDto;
};

export type UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponse = UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponses[keyof UpdateOrderAmountApiV1OrdersOrderIdAmountPutResponses];

export type AddOrderLinesApiV1OrdersOrderLinesBulkPostData = {
    body: BulkOrderLineCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/orders/order-lines/bulk';
};

export type AddOrderLinesApiV1OrdersOrderLinesBulkPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type AddOrderLinesApiV1OrdersOrderLinesBulkPostError = AddOrderLinesApiV1OrdersOrderLinesBulkPostErrors[keyof AddOrderLinesApiV1OrdersOrderLinesBulkPostErrors];

export type AddOrderLinesApiV1OrdersOrderLinesBulkPostResponses = {
    /**
     * Response Add Order Lines Api V1 Orders Order Lines Bulk Post
     * Successful Response
     */
    200: Array<OrderLineResponseDto>;
};

export type AddOrderLinesApiV1OrdersOrderLinesBulkPostResponse = AddOrderLinesApiV1OrdersOrderLinesBulkPostResponses[keyof AddOrderLinesApiV1OrdersOrderLinesBulkPostResponses];

export type UpdateProductionApiV1OrdersProductionPutData = {
    body: OrderProductionUpdateDto;
    path?: never;
    query?: never;
    url: '/api/v1/orders/production';
};

export type UpdateProductionApiV1OrdersProductionPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateProductionApiV1OrdersProductionPutError = UpdateProductionApiV1OrdersProductionPutErrors[keyof UpdateProductionApiV1OrdersProductionPutErrors];

export type UpdateProductionApiV1OrdersProductionPutResponses = {
    /**
     * Successful Response
     */
    200: OrderOperationResultDto;
};

export type UpdateProductionApiV1OrdersProductionPutResponse = UpdateProductionApiV1OrdersProductionPutResponses[keyof UpdateProductionApiV1OrdersProductionPutResponses];

export type GetOrderStatisticsApiV1OrdersStatisticsSummaryGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * User Id
         * Filter by user ID
         */
        user_id?: number | null;
    };
    url: '/api/v1/orders/statistics/summary';
};

export type GetOrderStatisticsApiV1OrdersStatisticsSummaryGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderStatisticsApiV1OrdersStatisticsSummaryGetError = GetOrderStatisticsApiV1OrdersStatisticsSummaryGetErrors[keyof GetOrderStatisticsApiV1OrdersStatisticsSummaryGetErrors];

export type GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponses = {
    /**
     * Successful Response
     */
    200: OrderStatisticsDto;
};

export type GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponse = GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponses[keyof GetOrderStatisticsApiV1OrdersStatisticsSummaryGetResponses];

export type GetDashboardDataApiV1OrdersDashboardDataGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * User Id
         * Filter by user ID
         */
        user_id?: number | null;
    };
    url: '/api/v1/orders/dashboard/data';
};

export type GetDashboardDataApiV1OrdersDashboardDataGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetDashboardDataApiV1OrdersDashboardDataGetError = GetDashboardDataApiV1OrdersDashboardDataGetErrors[keyof GetDashboardDataApiV1OrdersDashboardDataGetErrors];

export type GetDashboardDataApiV1OrdersDashboardDataGetResponses = {
    /**
     * Successful Response
     */
    200: OrderDashboardDto;
};

export type GetDashboardDataApiV1OrdersDashboardDataGetResponse = GetDashboardDataApiV1OrdersDashboardDataGetResponses[keyof GetDashboardDataApiV1OrdersDashboardDataGetResponses];

export type SearchOrderPartsApiV1OrderPartsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by order_part_no, part_name, etc.
         */
        search_term?: string | null;
        /**
         * Order No
         * Filter by order number
         */
        order_no?: string | null;
        /**
         * Part Type
         * Filter by part type
         */
        part_type?: string | null;
        /**
         * Status Filter
         * Filter by part status
         */
        status_filter?: string | null;
        /**
         * Supervisor User Id
         * Filter by supervisor user ID
         */
        supervisor_user_id?: number | null;
        /**
         * Color
         * Filter by color
         */
        color?: string | null;
        /**
         * Skip
         * Skip items
         */
        skip?: number;
        /**
         * Limit
         * Limit items
         */
        limit?: number;
    };
    url: '/api/v1/order-parts/';
};

export type SearchOrderPartsApiV1OrderPartsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SearchOrderPartsApiV1OrderPartsGetError = SearchOrderPartsApiV1OrderPartsGetErrors[keyof SearchOrderPartsApiV1OrderPartsGetErrors];

export type SearchOrderPartsApiV1OrderPartsGetResponses = {
    /**
     * Successful Response
     */
    200: OrderPartListDto;
};

export type SearchOrderPartsApiV1OrderPartsGetResponse = SearchOrderPartsApiV1OrderPartsGetResponses[keyof SearchOrderPartsApiV1OrderPartsGetResponses];

export type CreateOrderPartApiV1OrderPartsPostData = {
    body: OrderPartCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/order-parts/';
};

export type CreateOrderPartApiV1OrderPartsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrderPartApiV1OrderPartsPostError = CreateOrderPartApiV1OrderPartsPostErrors[keyof CreateOrderPartApiV1OrderPartsPostErrors];

export type CreateOrderPartApiV1OrderPartsPostResponses = {
    /**
     * Successful Response
     */
    200: OrderPartResponseDto;
};

export type CreateOrderPartApiV1OrderPartsPostResponse = CreateOrderPartApiV1OrderPartsPostResponses[keyof CreateOrderPartApiV1OrderPartsPostResponses];

export type GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/order-parts/order/{order_no}';
};

export type GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetError = GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetErrors[keyof GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetErrors];

export type GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponses = {
    /**
     * Response Get Order Parts By Order Api V1 Order Parts Order  Order No  Get
     * Successful Response
     */
    200: Array<OrderPartResponseDto>;
};

export type GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponse = GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponses[keyof GetOrderPartsByOrderApiV1OrderPartsOrderOrderNoGetResponses];

export type DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteData = {
    body?: never;
    path: {
        /**
         * Order Part Id
         */
        order_part_id: number;
    };
    query?: never;
    url: '/api/v1/order-parts/{order_part_id}';
};

export type DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteError = DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteErrors[keyof DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteErrors];

export type DeleteOrderPartApiV1OrderPartsOrderPartIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetOrderPartByIdApiV1OrderPartsOrderPartIdGetData = {
    body?: never;
    path: {
        /**
         * Order Part Id
         */
        order_part_id: number;
    };
    query?: never;
    url: '/api/v1/order-parts/{order_part_id}';
};

export type GetOrderPartByIdApiV1OrderPartsOrderPartIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderPartByIdApiV1OrderPartsOrderPartIdGetError = GetOrderPartByIdApiV1OrderPartsOrderPartIdGetErrors[keyof GetOrderPartByIdApiV1OrderPartsOrderPartIdGetErrors];

export type GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrderPartResponseDto;
};

export type GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponse = GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponses[keyof GetOrderPartByIdApiV1OrderPartsOrderPartIdGetResponses];

export type UpdateOrderPartApiV1OrderPartsOrderPartIdPutData = {
    body: OrderPartUpdateDto;
    path: {
        /**
         * Order Part Id
         */
        order_part_id: number;
    };
    query?: never;
    url: '/api/v1/order-parts/{order_part_id}';
};

export type UpdateOrderPartApiV1OrderPartsOrderPartIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderPartApiV1OrderPartsOrderPartIdPutError = UpdateOrderPartApiV1OrderPartsOrderPartIdPutErrors[keyof UpdateOrderPartApiV1OrderPartsOrderPartIdPutErrors];

export type UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponses = {
    /**
     * Successful Response
     */
    200: OrderPartResponseDto;
};

export type UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponse = UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponses[keyof UpdateOrderPartApiV1OrderPartsOrderPartIdPutResponses];

export type GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetData = {
    body?: never;
    path: {
        /**
         * Order Part Id
         */
        order_part_id: number;
    };
    query?: never;
    url: '/api/v1/order-parts/{order_part_id}/with-bundles';
};

export type GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetError = GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetErrors[keyof GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetErrors];

export type GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponses = {
    /**
     * Successful Response
     */
    200: OrderPartWithBundlesDto;
};

export type GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponse = GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponses[keyof GetOrderPartWithBundlesApiV1OrderPartsOrderPartIdWithBundlesGetResponses];

export type UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutData = {
    body: OrderPartStatusUpdateDto;
    path: {
        /**
         * Order Part Id
         */
        order_part_id: number;
    };
    query?: never;
    url: '/api/v1/order-parts/{order_part_id}/status';
};

export type UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutError = UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutErrors[keyof UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutErrors];

export type UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponses = {
    /**
     * Successful Response
     */
    200: OrderPartOperationResultDto;
};

export type UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponse = UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponses[keyof UpdateOrderPartStatusApiV1OrderPartsOrderPartIdStatusPutResponses];

export type BulkCreateOrderPartsApiV1OrderPartsBulkPostData = {
    body: BulkOrderPartCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/order-parts/bulk';
};

export type BulkCreateOrderPartsApiV1OrderPartsBulkPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type BulkCreateOrderPartsApiV1OrderPartsBulkPostError = BulkCreateOrderPartsApiV1OrderPartsBulkPostErrors[keyof BulkCreateOrderPartsApiV1OrderPartsBulkPostErrors];

export type BulkCreateOrderPartsApiV1OrderPartsBulkPostResponses = {
    /**
     * Response Bulk Create Order Parts Api V1 Order Parts Bulk Post
     * Successful Response
     */
    200: Array<OrderPartResponseDto>;
};

export type BulkCreateOrderPartsApiV1OrderPartsBulkPostResponse = BulkCreateOrderPartsApiV1OrderPartsBulkPostResponses[keyof BulkCreateOrderPartsApiV1OrderPartsBulkPostResponses];

export type GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Order No
         * Filter by order number
         */
        order_no?: string | null;
    };
    url: '/api/v1/order-parts/statistics/summary';
};

export type GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetError = GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetErrors[keyof GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetErrors];

export type GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponses = {
    /**
     * Successful Response
     */
    200: OrderPartStatisticsDto;
};

export type GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponse = GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponses[keyof GetOrderPartStatisticsApiV1OrderPartsStatisticsSummaryGetResponses];

export type SearchOrderBundlesApiV1OrderBundlesGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Search Term
         * Search by order_bundle_no, order_part_no, etc.
         */
        search_term?: string | null;
        /**
         * Order No
         * Filter by order number
         */
        order_no?: string | null;
        /**
         * Order Part No
         * Filter by order part number
         */
        order_part_no?: string | null;
        /**
         * Size
         * Filter by size
         */
        size?: string | null;
        /**
         * Status Filter
         * Filter by bundle status
         */
        status_filter?: string | null;
        /**
         * Color
         * Filter by color
         */
        color?: string | null;
        /**
         * Cutter User Id
         * Filter by cutter user ID
         */
        cutter_user_id?: number | null;
        /**
         * Sewer User Id
         * Filter by sewer user ID
         */
        sewer_user_id?: number | null;
        /**
         * Qc User Id
         * Filter by QC user ID
         */
        qc_user_id?: number | null;
        /**
         * Skip
         * Skip items
         */
        skip?: number;
        /**
         * Limit
         * Limit items
         */
        limit?: number;
    };
    url: '/api/v1/order-bundles/';
};

export type SearchOrderBundlesApiV1OrderBundlesGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SearchOrderBundlesApiV1OrderBundlesGetError = SearchOrderBundlesApiV1OrderBundlesGetErrors[keyof SearchOrderBundlesApiV1OrderBundlesGetErrors];

export type SearchOrderBundlesApiV1OrderBundlesGetResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleListDto;
};

export type SearchOrderBundlesApiV1OrderBundlesGetResponse = SearchOrderBundlesApiV1OrderBundlesGetResponses[keyof SearchOrderBundlesApiV1OrderBundlesGetResponses];

export type CreateOrderBundleApiV1OrderBundlesPostData = {
    body: OrderBundleCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/order-bundles/';
};

export type CreateOrderBundleApiV1OrderBundlesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrderBundleApiV1OrderBundlesPostError = CreateOrderBundleApiV1OrderBundlesPostErrors[keyof CreateOrderBundleApiV1OrderBundlesPostErrors];

export type CreateOrderBundleApiV1OrderBundlesPostResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleResponseDto;
};

export type CreateOrderBundleApiV1OrderBundlesPostResponse = CreateOrderBundleApiV1OrderBundlesPostResponses[keyof CreateOrderBundleApiV1OrderBundlesPostResponses];

export type GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetData = {
    body?: never;
    path: {
        /**
         * Order Part No
         */
        order_part_no: string;
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/order-bundles/order-part/{order_part_no}/order/{order_no}';
};

export type GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetError = GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetErrors[keyof GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetErrors];

export type GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponses = {
    /**
     * Response Get Order Bundles By Order Part Api V1 Order Bundles Order Part  Order Part No  Order  Order No  Get
     * Successful Response
     */
    200: Array<OrderBundleResponseDto>;
};

export type GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponse = GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponses[keyof GetOrderBundlesByOrderPartApiV1OrderBundlesOrderPartOrderPartNoOrderOrderNoGetResponses];

export type GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/order-bundles/order/{order_no}';
};

export type GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetError = GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetErrors[keyof GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetErrors];

export type GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponses = {
    /**
     * Response Get Order Bundles By Order Api V1 Order Bundles Order  Order No  Get
     * Successful Response
     */
    200: Array<OrderBundleResponseDto>;
};

export type GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponse = GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponses[keyof GetOrderBundlesByOrderApiV1OrderBundlesOrderOrderNoGetResponses];

export type DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteData = {
    body?: never;
    path: {
        /**
         * Order Bundle Id
         */
        order_bundle_id: number;
    };
    query?: never;
    url: '/api/v1/order-bundles/{order_bundle_id}';
};

export type DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteError = DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteErrors[keyof DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteErrors];

export type DeleteOrderBundleApiV1OrderBundlesOrderBundleIdDeleteResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetData = {
    body?: never;
    path: {
        /**
         * Order Bundle Id
         */
        order_bundle_id: number;
    };
    query?: never;
    url: '/api/v1/order-bundles/{order_bundle_id}';
};

export type GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetError = GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetErrors[keyof GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetErrors];

export type GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleResponseDto;
};

export type GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponse = GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponses[keyof GetOrderBundleByIdApiV1OrderBundlesOrderBundleIdGetResponses];

export type UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutData = {
    body: OrderBundleUpdateDto;
    path: {
        /**
         * Order Bundle Id
         */
        order_bundle_id: number;
    };
    query?: never;
    url: '/api/v1/order-bundles/{order_bundle_id}';
};

export type UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutError = UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutErrors[keyof UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutErrors];

export type UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleResponseDto;
};

export type UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponse = UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponses[keyof UpdateOrderBundleApiV1OrderBundlesOrderBundleIdPutResponses];

export type UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutData = {
    body: OrderBundleStatusUpdateDto;
    path: {
        /**
         * Order Bundle Id
         */
        order_bundle_id: number;
    };
    query?: never;
    url: '/api/v1/order-bundles/{order_bundle_id}/status';
};

export type UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutError = UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutErrors[keyof UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutErrors];

export type UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleOperationResultDto;
};

export type UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponse = UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponses[keyof UpdateOrderBundleStatusApiV1OrderBundlesOrderBundleIdStatusPutResponses];

export type UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutData = {
    body: OrderBundleProductionUpdateDto;
    path: {
        /**
         * Order Bundle Id
         */
        order_bundle_id: number;
    };
    query?: never;
    url: '/api/v1/order-bundles/{order_bundle_id}/production';
};

export type UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutError = UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutErrors[keyof UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutErrors];

export type UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleOperationResultDto;
};

export type UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponse = UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponses[keyof UpdateOrderBundleProductionApiV1OrderBundlesOrderBundleIdProductionPutResponses];

export type BulkCreateOrderBundlesApiV1OrderBundlesBulkPostData = {
    body: BulkOrderBundleCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/order-bundles/bulk';
};

export type BulkCreateOrderBundlesApiV1OrderBundlesBulkPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type BulkCreateOrderBundlesApiV1OrderBundlesBulkPostError = BulkCreateOrderBundlesApiV1OrderBundlesBulkPostErrors[keyof BulkCreateOrderBundlesApiV1OrderBundlesBulkPostErrors];

export type BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponses = {
    /**
     * Response Bulk Create Order Bundles Api V1 Order Bundles Bulk Post
     * Successful Response
     */
    200: Array<OrderBundleResponseDto>;
};

export type BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponse = BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponses[keyof BulkCreateOrderBundlesApiV1OrderBundlesBulkPostResponses];

export type GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Order No
         * Filter by order number
         */
        order_no?: string | null;
        /**
         * Order Part No
         * Filter by order part number
         */
        order_part_no?: string | null;
    };
    url: '/api/v1/order-bundles/statistics/summary';
};

export type GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetError = GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetErrors[keyof GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetErrors];

export type GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponses = {
    /**
     * Successful Response
     */
    200: OrderBundleStatisticsDto;
};

export type GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponse = GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponses[keyof GetOrderBundleStatisticsApiV1OrderBundlesStatisticsSummaryGetResponses];

export type GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}/crafts';
};

export type GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetError = GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetErrors[keyof GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetErrors];

export type GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponses = {
    /**
     * Response Get Order Crafts By Order Api V1 Orders  Order No  Crafts Get
     * Successful Response
     */
    200: Array<OrderCraftResponseDto>;
};

export type GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponse = GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponses[keyof GetOrderCraftsByOrderApiV1OrdersOrderNoCraftsGetResponses];

export type CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostData = {
    body: OrderCraftWorkflowDto;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}/crafts';
};

export type CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostError = CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostErrors[keyof CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostErrors];

export type CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponses = {
    /**
     * Response Create Order Crafts For Order Api V1 Orders  Order No  Crafts Post
     * Successful Response
     */
    200: Array<OrderCraftResponseDto>;
};

export type CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponse = CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponses[keyof CreateOrderCraftsForOrderApiV1OrdersOrderNoCraftsPostResponses];

export type GetOrderCraftByIdApiV1CraftsOrderCraftIdGetData = {
    body?: never;
    path: {
        /**
         * Order Craft Id
         */
        order_craft_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/{order_craft_id}';
};

export type GetOrderCraftByIdApiV1CraftsOrderCraftIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderCraftByIdApiV1CraftsOrderCraftIdGetError = GetOrderCraftByIdApiV1CraftsOrderCraftIdGetErrors[keyof GetOrderCraftByIdApiV1CraftsOrderCraftIdGetErrors];

export type GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftResponseDto;
};

export type GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponse = GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponses[keyof GetOrderCraftByIdApiV1CraftsOrderCraftIdGetResponses];

export type UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutData = {
    body: OrderCraftStatusUpdateDto;
    path: {
        /**
         * Order Craft Id
         */
        order_craft_id: number;
    };
    query?: never;
    url: '/api/v1/crafts/{order_craft_id}/status';
};

export type UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutError = UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutErrors[keyof UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutErrors];

export type UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftOperationResultDto;
};

export type UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponse = UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponses[keyof UpdateOrderCraftStatusApiV1CraftsOrderCraftIdStatusPutResponses];

export type UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutData = {
    body: OrderCraftRouteStatusUpdateDto;
    path: {
        /**
         * Order Craft Route Id
         */
        order_craft_route_id: number;
    };
    query?: never;
    url: '/api/v1/craft-routes/{order_craft_route_id}/status';
};

export type UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutError = UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutErrors[keyof UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutErrors];

export type UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftOperationResultDto;
};

export type UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponse = UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponses[keyof UpdateOrderCraftRouteStatusApiV1CraftRoutesOrderCraftRouteIdStatusPutResponses];

export type GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}/crafts/next';
};

export type GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetError = GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetErrors[keyof GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetErrors];

export type GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftResponseDto;
};

export type GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponse = GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponses[keyof GetNextCraftForOrderApiV1OrdersOrderNoCraftsNextGetResponses];

export type GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/orders/{order_no}/crafts/current';
};

export type GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetError = GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetErrors[keyof GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetErrors];

export type GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftResponseDto;
};

export type GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponse = GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponses[keyof GetCurrentCraftForOrderApiV1OrdersOrderNoCraftsCurrentGetResponses];

export type GetOrderCraftStatisticsApiV1StatisticsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Order No
         * Filter by order number
         */
        order_no?: string | null;
    };
    url: '/api/v1/statistics';
};

export type GetOrderCraftStatisticsApiV1StatisticsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetOrderCraftStatisticsApiV1StatisticsGetError = GetOrderCraftStatisticsApiV1StatisticsGetErrors[keyof GetOrderCraftStatisticsApiV1StatisticsGetErrors];

export type GetOrderCraftStatisticsApiV1StatisticsGetResponses = {
    /**
     * Successful Response
     */
    200: OrderCraftStatisticsDto;
};

export type GetOrderCraftStatisticsApiV1StatisticsGetResponse = GetOrderCraftStatisticsApiV1StatisticsGetResponses[keyof GetOrderCraftStatisticsApiV1StatisticsGetResponses];

export type CreateCraftInstanceApiV1CraftInstancesPostData = {
    body: CraftInstanceCreateDto;
    path?: never;
    query?: never;
    url: '/api/v1/craft-instances/';
};

export type CreateCraftInstanceApiV1CraftInstancesPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateCraftInstanceApiV1CraftInstancesPostError = CreateCraftInstanceApiV1CraftInstancesPostErrors[keyof CreateCraftInstanceApiV1CraftInstancesPostErrors];

export type CreateCraftInstanceApiV1CraftInstancesPostResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceOperationResultDto;
};

export type CreateCraftInstanceApiV1CraftInstancesPostResponse = CreateCraftInstanceApiV1CraftInstancesPostResponses[keyof CreateCraftInstanceApiV1CraftInstancesPostResponses];

export type QrScanRegisterApiV1CraftInstancesQrScanPostData = {
    body: CraftInstanceQrScanDto;
    path?: never;
    query?: never;
    url: '/api/v1/craft-instances/qr-scan';
};

export type QrScanRegisterApiV1CraftInstancesQrScanPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type QrScanRegisterApiV1CraftInstancesQrScanPostError = QrScanRegisterApiV1CraftInstancesQrScanPostErrors[keyof QrScanRegisterApiV1CraftInstancesQrScanPostErrors];

export type QrScanRegisterApiV1CraftInstancesQrScanPostResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceOperationResultDto;
};

export type QrScanRegisterApiV1CraftInstancesQrScanPostResponse = QrScanRegisterApiV1CraftInstancesQrScanPostResponses[keyof QrScanRegisterApiV1CraftInstancesQrScanPostResponses];

export type SearchCraftInstancesApiV1CraftInstancesSearchGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Order No
         * 订单号
         */
        order_no?: string | null;
        /**
         * Worker User Id
         * 工人ID
         */
        worker_user_id?: number | null;
        /**
         * Completion Granularity
         * 完成粒度
         */
        completion_granularity?: CompletionGranularityDto | null;
        /**
         * Instance Status
         * 状态
         */
        instance_status?: string | null;
        /**
         * Settlement Status
         * 结算状态
         */
        settlement_status?: SettlementStatusDto | null;
        /**
         * Quality Level
         * 质量等级
         */
        quality_level?: string | null;
        /**
         * Limit
         * 每页数量
         */
        limit?: number;
        /**
         * Offset
         * 偏移量
         */
        offset?: number;
    };
    url: '/api/v1/craft-instances/search';
};

export type SearchCraftInstancesApiV1CraftInstancesSearchGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SearchCraftInstancesApiV1CraftInstancesSearchGetError = SearchCraftInstancesApiV1CraftInstancesSearchGetErrors[keyof SearchCraftInstancesApiV1CraftInstancesSearchGetErrors];

export type SearchCraftInstancesApiV1CraftInstancesSearchGetResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceListDto;
};

export type SearchCraftInstancesApiV1CraftInstancesSearchGetResponse = SearchCraftInstancesApiV1CraftInstancesSearchGetResponses[keyof SearchCraftInstancesApiV1CraftInstancesSearchGetResponses];

export type GetCraftInstanceApiV1CraftInstancesInstanceIdGetData = {
    body?: never;
    path: {
        /**
         * Instance Id
         */
        instance_id: number;
    };
    query?: never;
    url: '/api/v1/craft-instances/{instance_id}';
};

export type GetCraftInstanceApiV1CraftInstancesInstanceIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCraftInstanceApiV1CraftInstancesInstanceIdGetError = GetCraftInstanceApiV1CraftInstancesInstanceIdGetErrors[keyof GetCraftInstanceApiV1CraftInstancesInstanceIdGetErrors];

export type GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceResponseDto;
};

export type GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponse = GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponses[keyof GetCraftInstanceApiV1CraftInstancesInstanceIdGetResponses];

export type VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostData = {
    body: CraftInstanceVerificationDto;
    path: {
        /**
         * Instance Id
         */
        instance_id: number;
    };
    query?: never;
    url: '/api/v1/craft-instances/{instance_id}/verify';
};

export type VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostError = VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostErrors[keyof VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostErrors];

export type VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceOperationResultDto;
};

export type VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponse = VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponses[keyof VerifyCraftInstanceApiV1CraftInstancesInstanceIdVerifyPostResponses];

export type RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostData = {
    body: CraftInstanceRejectionDto;
    path: {
        /**
         * Instance Id
         */
        instance_id: number;
    };
    query?: never;
    url: '/api/v1/craft-instances/{instance_id}/reject';
};

export type RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostError = RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostErrors[keyof RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostErrors];

export type RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceOperationResultDto;
};

export type RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponse = RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponses[keyof RejectCraftInstanceApiV1CraftInstancesInstanceIdRejectPostResponses];

export type GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Order No
         * 订单号
         */
        order_no?: string | null;
        /**
         * Worker User Id
         * 工人ID
         */
        worker_user_id?: number | null;
    };
    url: '/api/v1/craft-instances/statistics/overview';
};

export type GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetError = GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetErrors[keyof GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetErrors];

export type GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponses = {
    /**
     * Successful Response
     */
    200: CraftInstanceStatisticsDto;
};

export type GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponse = GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponses[keyof GetCraftInstanceStatisticsApiV1CraftInstancesStatisticsOverviewGetResponses];

export type GetAvailableRegistrationDataData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: {
        /**
         * Craft Route Id
         * 筛选指定工艺路线ID
         */
        craft_route_id?: number | null;
        /**
         * Include Completed Routes
         * 是否包含已完成的路线
         */
        include_completed_routes?: boolean;
        /**
         * Granularity Filter
         * 粒度过滤: order/part/bundle
         */
        granularity_filter?: string | null;
    };
    url: '/api/v1/available-registration/{order_no}';
};

export type GetAvailableRegistrationDataErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetAvailableRegistrationDataError = GetAvailableRegistrationDataErrors[keyof GetAvailableRegistrationDataErrors];

export type GetAvailableRegistrationDataResponses = {
    /**
     * Successful Response
     */
    200: AvailableRegistrationDto;
};

export type GetAvailableRegistrationDataResponse = GetAvailableRegistrationDataResponses[keyof GetAvailableRegistrationDataResponses];

export type GetRegistrationSummaryData = {
    body?: never;
    path: {
        /**
         * Order No
         */
        order_no: string;
    };
    query?: never;
    url: '/api/v1/available-registration/{order_no}/summary';
};

export type GetRegistrationSummaryErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetRegistrationSummaryError = GetRegistrationSummaryErrors[keyof GetRegistrationSummaryErrors];

export type GetRegistrationSummaryResponses = {
    /**
     * Successful Response
     */
    200: RegistrationSummaryDto;
};

export type GetRegistrationSummaryResponse = GetRegistrationSummaryResponses[keyof GetRegistrationSummaryResponses];

export type GetBillsApiV1BillsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Worker User Id
         * Worker user ID
         */
        worker_user_id?: number | null;
        /**
         * Status
         * Bill status
         */
        status?: BillStatus | null;
        /**
         * Bill Date
         * Bill date
         */
        bill_date?: string | null;
        /**
         * Start Date
         * Start date for date range search
         */
        start_date?: string | null;
        /**
         * End Date
         * End date for date range search
         */
        end_date?: string | null;
        /**
         * Skip
         * Number of records to skip
         */
        skip?: number;
        /**
         * Limit
         * Number of records to return
         */
        limit?: number;
    };
    url: '/api/v1/bills/';
};

export type GetBillsApiV1BillsGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetBillsApiV1BillsGetError = GetBillsApiV1BillsGetErrors[keyof GetBillsApiV1BillsGetErrors];

export type GetBillsApiV1BillsGetResponses = {
    /**
     * Successful Response
     */
    200: BillListResponseDto;
};

export type GetBillsApiV1BillsGetResponse = GetBillsApiV1BillsGetResponses[keyof GetBillsApiV1BillsGetResponses];

export type CreateBillApiV1BillsPostData = {
    body: CreateBillRequestDto;
    path?: never;
    query?: never;
    url: '/api/v1/bills/';
};

export type CreateBillApiV1BillsPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CreateBillApiV1BillsPostError = CreateBillApiV1BillsPostErrors[keyof CreateBillApiV1BillsPostErrors];

export type CreateBillApiV1BillsPostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type CreateBillApiV1BillsPostResponse = CreateBillApiV1BillsPostResponses[keyof CreateBillApiV1BillsPostResponses];

export type GetBillApiV1BillsBillIdGetData = {
    body?: never;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}';
};

export type GetBillApiV1BillsBillIdGetErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type GetBillApiV1BillsBillIdGetError = GetBillApiV1BillsBillIdGetErrors[keyof GetBillApiV1BillsBillIdGetErrors];

export type GetBillApiV1BillsBillIdGetResponses = {
    /**
     * Successful Response
     */
    200: BillResponseDto;
};

export type GetBillApiV1BillsBillIdGetResponse = GetBillApiV1BillsBillIdGetResponses[keyof GetBillApiV1BillsBillIdGetResponses];

export type UpdateBillApiV1BillsBillIdPutData = {
    body: UpdateBillRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}';
};

export type UpdateBillApiV1BillsBillIdPutErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type UpdateBillApiV1BillsBillIdPutError = UpdateBillApiV1BillsBillIdPutErrors[keyof UpdateBillApiV1BillsBillIdPutErrors];

export type UpdateBillApiV1BillsBillIdPutResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type UpdateBillApiV1BillsBillIdPutResponse = UpdateBillApiV1BillsBillIdPutResponses[keyof UpdateBillApiV1BillsBillIdPutResponses];

export type SubmitBillForReviewApiV1BillsBillIdSubmitPostData = {
    body: BillSubmitForReviewRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}/submit';
};

export type SubmitBillForReviewApiV1BillsBillIdSubmitPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type SubmitBillForReviewApiV1BillsBillIdSubmitPostError = SubmitBillForReviewApiV1BillsBillIdSubmitPostErrors[keyof SubmitBillForReviewApiV1BillsBillIdSubmitPostErrors];

export type SubmitBillForReviewApiV1BillsBillIdSubmitPostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type SubmitBillForReviewApiV1BillsBillIdSubmitPostResponse = SubmitBillForReviewApiV1BillsBillIdSubmitPostResponses[keyof SubmitBillForReviewApiV1BillsBillIdSubmitPostResponses];

export type ApproveBillApiV1BillsBillIdApprovePostData = {
    body: BillApprovalRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}/approve';
};

export type ApproveBillApiV1BillsBillIdApprovePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ApproveBillApiV1BillsBillIdApprovePostError = ApproveBillApiV1BillsBillIdApprovePostErrors[keyof ApproveBillApiV1BillsBillIdApprovePostErrors];

export type ApproveBillApiV1BillsBillIdApprovePostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type ApproveBillApiV1BillsBillIdApprovePostResponse = ApproveBillApiV1BillsBillIdApprovePostResponses[keyof ApproveBillApiV1BillsBillIdApprovePostResponses];

export type RejectBillApiV1BillsBillIdRejectPostData = {
    body: BillRejectionRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}/reject';
};

export type RejectBillApiV1BillsBillIdRejectPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type RejectBillApiV1BillsBillIdRejectPostError = RejectBillApiV1BillsBillIdRejectPostErrors[keyof RejectBillApiV1BillsBillIdRejectPostErrors];

export type RejectBillApiV1BillsBillIdRejectPostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type RejectBillApiV1BillsBillIdRejectPostResponse = RejectBillApiV1BillsBillIdRejectPostResponses[keyof RejectBillApiV1BillsBillIdRejectPostResponses];

export type MarkBillAsPaidApiV1BillsBillIdPayPostData = {
    body: BillPaymentRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}/pay';
};

export type MarkBillAsPaidApiV1BillsBillIdPayPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type MarkBillAsPaidApiV1BillsBillIdPayPostError = MarkBillAsPaidApiV1BillsBillIdPayPostErrors[keyof MarkBillAsPaidApiV1BillsBillIdPayPostErrors];

export type MarkBillAsPaidApiV1BillsBillIdPayPostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type MarkBillAsPaidApiV1BillsBillIdPayPostResponse = MarkBillAsPaidApiV1BillsBillIdPayPostResponses[keyof MarkBillAsPaidApiV1BillsBillIdPayPostResponses];

export type CancelBillApiV1BillsBillIdCancelPostData = {
    body: BillCancellationRequestDto;
    path: {
        /**
         * Bill Id
         */
        bill_id: number;
    };
    query?: never;
    url: '/api/v1/bills/{bill_id}/cancel';
};

export type CancelBillApiV1BillsBillIdCancelPostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type CancelBillApiV1BillsBillIdCancelPostError = CancelBillApiV1BillsBillIdCancelPostErrors[keyof CancelBillApiV1BillsBillIdCancelPostErrors];

export type CancelBillApiV1BillsBillIdCancelPostResponses = {
    /**
     * Successful Response
     */
    200: BillOperationResponseDto;
};

export type CancelBillApiV1BillsBillIdCancelPostResponse = CancelBillApiV1BillsBillIdCancelPostResponses[keyof CancelBillApiV1BillsBillIdCancelPostResponses];

export type GetSettlementSummaryApiV1BillsSettlementSummaryGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/bills/settlement/summary';
};

export type GetSettlementSummaryApiV1BillsSettlementSummaryGetResponses = {
    /**
     * Successful Response
     */
    200: BillSettlementSummaryDto;
};

export type GetSettlementSummaryApiV1BillsSettlementSummaryGetResponse = GetSettlementSummaryApiV1BillsSettlementSummaryGetResponses[keyof GetSettlementSummaryApiV1BillsSettlementSummaryGetResponses];

export type DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostData = {
    body: InstanceDisputeRequestDto;
    path: {
        /**
         * Instance Id
         */
        instance_id: number;
    };
    query?: never;
    url: '/api/v1/bills/instances/{instance_id}/dispute';
};

export type DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostError = DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostErrors[keyof DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostErrors];

export type DisputeInstanceApiV1BillsInstancesInstanceIdDisputePostResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostData = {
    body: InstanceDisputeResolutionRequestDto;
    path: {
        /**
         * Instance Id
         */
        instance_id: number;
    };
    query?: never;
    url: '/api/v1/bills/instances/{instance_id}/resolve-dispute';
};

export type ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostError = ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostErrors[keyof ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostErrors];

export type ResolveInstanceDisputeApiV1BillsInstancesInstanceIdResolveDisputePostResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type GetPendingBillsApiV1BillsPendingGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/v1/bills/pending';
};

export type GetPendingBillsApiV1BillsPendingGetResponses = {
    /**
     * Successful Response
     */
    200: BillListResponseDto;
};

export type GetPendingBillsApiV1BillsPendingGetResponse = GetPendingBillsApiV1BillsPendingGetResponses[keyof GetPendingBillsApiV1BillsPendingGetResponses];

export type RootGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/';
};

export type RootGetResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type HealthCheckHealthGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/health';
};

export type HealthCheckHealthGetResponses = {
    /**
     * Successful Response
     */
    200: unknown;
};

export type ClientOptions = {
    baseURL: `${string}://${string}` | (string & {});
};